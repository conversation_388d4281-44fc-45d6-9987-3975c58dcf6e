<template>
  <div class="light-container">
    <div v-for="(item, index) in lightMode" :key="item.idx" class="item-container"
      :style="{ width: `${70 / lightMode.length}%` }">
      <div :class="item.name ? (item.idx === props.light.mode ? 'item cw-active' : 'item') : 'item cw-disable'"
        @click="handleLightClick(item)">{{ index + 1 }}</div>
      <div :class="item.name ? 'name' : 'name light'">{{ item.name || '-' }}</div>
    </div>
    <div class="item-container" style="width: 30%;">
      <div class="opt">
        <div v-for="item in triggerMode" :key="item.idx" :class="item.isActive ? 'mode cw-active' : 'mode'"
          @click="handleTriggerClick(item)">{{ item.name }}</div>
      </div>
      <div class="config" @click="handleOpenConfig">
        <span class="iconfont icon-shezhi"></span>
      </div>
    </div>
  </div>
  <el-dialog :title="t('control.light.title')" v-model="isOpen" width="30%">
    <el-form :model="lightParams" ref="config" label-width="180px">
      <el-form-item prop="delay" :label="t('control.light.delay')">
        <el-input v-model="lightParams.delay" type="number"
          :placeholder="t('common.enterPrefix') + t('control.light.delay')"></el-input>
      </el-form-item>
      <el-form-item prop="brightness" :label="t('control.light.brightness')">
        <el-input v-model="lightParams.brightness"
          :placeholder="t('common.enterPrefix') + t('control.light.brightness')"></el-input>
      </el-form-item>
      <el-form-item prop="duration" :label="t('control.light.duration')">
        <el-input v-model="lightParams.duration"
          :placeholder="t('common.enterPrefix') + t('control.light.duration')"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button @click="handleOk">{{ t('common.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElButton } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { ModeType, LightParamsType, Light } from './type'

const { t } = useI18n()

const lightMode = ref<ModeType[]>([
  {
    idx: 1,
    name: 'Off',
  },
  {
    idx: 2,
    name: 'On',
  },
  {
    idx: 3,
    name: 'Pulse',
  },
])

const triggerMode = ref<ModeType[]>([
  {
    idx: 1,
    name: 'IO',
    isActive: false,
  },
  {
    idx: 2,
    name: 'Int',
    isActive: false,
  },
  {
    idx: 3,
    name: 'S.T.',
    isActive: false,
  },
])

const isOpen = ref(false)
const config = ref(null)

const lightParams = defineModel<LightParamsType>('lightParams', {
  required: true,
  default: () => ({
    delay: 0,
    brightness: 0,
    duration: 0,
  }),
})

const props = defineProps<{
  light: Light
}>()

const emit = defineEmits(['light', 'trigger', 'get', 'set'])

const handleLightClick = (item: ModeType) => {
  emit('light', item.idx)
}

const handleTriggerClick = (item: ModeType) => {
  triggerMode.value.forEach((v, i, arr) => { arr[i].isActive = false })
  item.isActive = true
  emit('trigger', item.idx)
}

const handleOpenConfig = () => {
  isOpen.value = true
  emit('get')
}

const handleCancel = () => {
  isOpen.value = false
}

const handleOk = () => {
  emit('set', lightParams.value)
  isOpen.value = false
}

</script>

<style lang="less" scoped>
.light-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  gap: @cw-gap;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 80%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .opt {
      width: 100%;
      height: 80%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .mode {
        width: 100%;
        height: 30%;
        border: 1px solid @cw-border-color;
        border-radius: @cw-gap;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: @cw-border-color-active;
          color: @cw-font-color-title;
        }
      }
    }

    .config {
      width: 100%;
      height: 15%;
      display: flex;
      color: @cw-font-color;
      border: 1px solid @cw-border-color;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

  }
}
</style>
