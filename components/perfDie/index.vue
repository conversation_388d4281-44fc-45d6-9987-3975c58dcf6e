<template>
  <div class="perf-die" ref="container" @mousedown="handleMouseDown" @mousemove="handleMouseMove"
    @mouseup="handleMouseUp" @mouseleave="handleMouseUp" @wheel="handleWheel">
    <div class="canvas-container" :style="{
      transform: `translate(${offset.x}px, ${offset.y}px) scale(${scale})`,
      transformOrigin: '0 0'
    }">
      <canvas ref="staticLayer" class="canvas-layer"></canvas>
      <canvas ref="defectLayer" class="canvas-layer"></canvas>
      <canvas ref="interactionLayer" class="canvas-layer" @click="handleCanvasClick"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, computed, watch } from 'vue'

const props = defineProps<{
  width: number,
  height: number,
  defects: {
    id: string,
    x: number,
    y: number,
    w: number,
    h: number,
    color: string,
  }[]
}>()

// 计算比例
const ratio = computed(() => props.width / props.height)

const emit = defineEmits<{
  (e: 'defectChange', id: string): void
}>()

// 高亮指定ID的缺陷
const highlightDefect = (id: string | null) => {
  selectedDefectId.value = id
  drawDefects()
}

// 引用元素
const container = ref<HTMLDivElement | null>(null)
const staticLayer = ref<HTMLCanvasElement | null>(null)
const defectLayer = ref<HTMLCanvasElement | null>(null)
const interactionLayer = ref<HTMLCanvasElement | null>(null)

// 状态
const canvasSize = reactive({
  width: 0,
  height: 0
})
const selectedDefectId = ref<string | null>(null)
const defectRects = ref<{ id: string, rect: { x: number, y: number, w: number, h: number } }[]>([])

// 缩放和平移相关状态
const scale = ref(1)
const offset = reactive({ x: 0, y: 0 })
const isDragging = ref(false)
const lastMousePos = reactive({ x: 0, y: 0 })
const minScale = 0.8
const maxScale = 10

// 初始化canvas尺寸
const initCanvasSize = () => {
  if (!container.value) return

  // 获取容器尺寸
  let containerWidth = container.value.clientWidth
  let containerHeight = container.value.clientHeight

  // 如果容器尺寸为0，使用父元素尺寸或设置一个默认值
  if (containerWidth === 0 || containerHeight === 0) {
    const parentElement = container.value.parentElement
    if (parentElement) {
      containerWidth = parentElement.clientWidth || 300
      containerHeight = parentElement.clientHeight || 300
    } else {
      containerWidth = 300
      containerHeight = 300
    }
  }

  // 根据容器尺寸和比例计算canvas尺寸
  let canvasWidth, canvasHeight
  if (containerWidth / containerHeight > ratio.value) {
    canvasHeight = containerHeight
    canvasWidth = canvasHeight * ratio.value
  } else {
    canvasWidth = containerWidth
    canvasHeight = canvasWidth / ratio.value
  }

  // 确保尺寸至少为1
  canvasSize.width = Math.max(1, canvasWidth)
  canvasSize.height = Math.max(1, canvasHeight)

  // 设置所有canvas的尺寸
  const layers = [staticLayer.value, defectLayer.value, interactionLayer.value]
  layers.forEach(canvas => {
    if (!canvas) return

    // 设置设备像素比，解决模糊问题
    const dpr = window.devicePixelRatio || 1
    canvas.width = canvasSize.width * dpr
    canvas.height = canvasSize.height * dpr
    canvas.style.width = `${canvasSize.width}px`
    canvas.style.height = `${canvasSize.height}px`

    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.scale(dpr, dpr)
      // 增加抗锯齿设置
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'
    }
  })
}

// 绘制die线框（静态层）
const drawDieFrame = () => {
  const canvas = staticLayer.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvasSize.width, canvasSize.height)

  // 绘制die边框
  ctx.strokeStyle = 'rgba(0, 205, 245, 0.9)'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, canvasSize.width, canvasSize.height)
}

// 计算defect在canvas上的实际位置和大小
const calculateDefectPositions = () => {
  if (canvasSize.width === 0 || canvasSize.height === 0) return

  const scaleX = canvasSize.width / props.width
  const scaleY = canvasSize.height / props.height

  defectRects.value = props.defects.map(defect => {
    return {
      id: defect.id,
      rect: {
        x: defect.x * scaleX,
        y: defect.y * scaleY,
        w: defect.w * scaleX,
        h: defect.h * scaleY
      }
    }
  })
}

// 绘制缺陷（动态层）
const drawDefects = () => {
  const canvas = defectLayer.value
  if (!canvas) return

  const ctx = canvas.getContext('2d', { alpha: true })
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvasSize.width, canvasSize.height)

  // 启用抗锯齿
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = 'high'

  // 绘制每个缺陷
  props.defects.forEach((defect, index) => {
    const defectRect = defectRects.value[index]
    if (!defectRect) return

    const { x, y, w, h } = defectRect.rect

    // 绘制缺陷
    ctx.fillStyle = defect.color
    ctx.strokeStyle = defect.color
    ctx.lineWidth = 5
    ctx.fillRect(x, y, w, h)
    ctx.strokeRect(x, y, w, h)

    // 如果是选中的缺陷，绘制高亮边框
    if (selectedDefectId.value === defect.id) {
      ctx.strokeStyle = '#fF0'
      ctx.lineWidth = 5
      ctx.strokeRect(x, y, w, h)
    }
  })
}

// 处理鼠标点击事件
const handleMouseDown = (event: MouseEvent) => {
  if (!container.value) return

  const rect = container.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 记录初始位置用于拖拽
  isDragging.value = true
  lastMousePos.x = x
  lastMousePos.y = y
}

// 处理画布点击事件
const handleCanvasClick = (event: MouseEvent) => {
  if (!container.value || !interactionLayer.value) return

  // 获取canvas的位置
  const canvasRect = interactionLayer.value.getBoundingClientRect()
  const x = event.clientX - canvasRect.left
  const y = event.clientY - canvasRect.top


  // 直接计算相对于canvas的坐标
  const canvasX = x / scale.value
  const canvasY = y / scale.value

  // 检查是否点击了缺陷
  for (const defectRect of defectRects.value) {
    const { id, rect } = defectRect
    if (
      Math.round(canvasX) >= Math.round(rect.x - 5) &&
      Math.round(canvasX) <= Math.round(rect.x + rect.w + 5) &&
      Math.round(canvasY) >= Math.round(rect.y - 5) &&
      Math.round(canvasY) <= Math.round(rect.y + rect.h + 5)
    ) {
      highlightDefect(id)
      emit('defectChange', id)
      return
    }
  }

}

// 处理鼠标移动事件
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !container.value) return

  const rect = container.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 计算移动差值
  const dx = x - lastMousePos.x
  const dy = y - lastMousePos.y

  // 更新偏移
  offset.x += dx
  offset.y += dy

  // 更新最后位置
  lastMousePos.x = x
  lastMousePos.y = y
}

// 处理鼠标松开事件
const handleMouseUp = () => {
  isDragging.value = false
}

// 处理鼠标滚轮事件
const handleWheel = (event: WheelEvent) => {
  if (!container.value) return

  // 阻止默认滚动行为
  event.preventDefault()

  const rect = container.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 计算鼠标位置相对于当前变换的坐标
  const mouseOrigX = (mouseX - offset.x) / scale.value
  const mouseOrigY = (mouseY - offset.y) / scale.value

  // 根据滚轮方向决定缩放方向
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.min(maxScale, Math.max(minScale, scale.value + delta))

  // 如果缩放比例不变，则返回
  if (newScale === scale.value) return

  // 应用新的缩放比例
  scale.value = newScale

  // 调整偏移，使鼠标位置保持在同一点上
  offset.x = mouseX - mouseOrigX * scale.value
  offset.y = mouseY - mouseOrigY * scale.value
}

// 初始化并绘制
const initAndDraw = () => {
  initCanvasSize()
  drawDieFrame()
  calculateDefectPositions()
  drawDefects()
}

watch(() => props.defects, () => {
  calculateDefectPositions()
  drawDefects()
})

// 重置视图
const resetView = () => {
  scale.value = 1
  offset.x = 0
  offset.y = 0
}

// 组件挂载后初始化
onMounted(() => {

  // 监听窗口大小变化
  window.addEventListener('resize', initAndDraw)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', initAndDraw)
})

// 暴露方法给父组件
defineExpose({
  highlightDefect,
  resetView,
  initAndDraw
})
</script>

<style scoped lang="less">
.perf-die {
  width: 100%;
  height: 100%;
  position: relative;
  min-width: 100px;
  min-height: 100px;
  overflow: hidden;
  cursor: move;
}

.canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.05s ease-out;
  will-change: transform;
}

.canvas-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
</style>
