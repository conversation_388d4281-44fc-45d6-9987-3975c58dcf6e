<template>
  <div class="filter-container">
    <div v-for="(item, index) in filters" :key="item.idx" class="item-container"
      :style="{ width: `${100 / filters.length}%` }">
      <div :class="item.name ? (item.idx === props.filter.idx ? 'item cw-active' : 'item') : 'item cw-disable'"
        @click="handleFilterClick(item)">{{ item.name || '-' }}</div>
      <div :class="item.name ? 'name' : 'name light'">{{ item.idx }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FilterItemType, Filter, FilterListItemType } from './type'

const props = defineProps<{
  filter: Filter,
  filterList: FilterListItemType[]
}>()

const initFilter = props.filterList.map(item => ({
  idx: item.num,
  name: item.enable ? item.name : '',
  isActive: false,
}))

const filters = ref<FilterItemType[]>([...initFilter])

const emit = defineEmits(['send', 'change'])

const handleFilterClick = (item: FilterItemType) => {
  if (!item.name) return
  emit('send', item)
  emit('change', item.idx)
}
</script>

<style lang="less" scoped>
.filter-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  gap: @cw-gap;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 80%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .light {
      background-color: @cw-background-color;
    }

  }


}
</style>
