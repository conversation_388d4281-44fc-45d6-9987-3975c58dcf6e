/**
 * WebGL渲染器
 * 用于高性能图像渲染
 */

// 顶点着色器
const VERTEX_SHADER = `
  attribute vec2 a_position;
  attribute vec2 a_texCoord;
  varying vec2 v_texCoord;

  void main() {
    gl_Position = vec4(a_position, 0, 1);
    v_texCoord = a_texCoord;
  }
`;

// 片段着色器
const FRAGMENT_SHADER = `
  precision mediump float;
  uniform sampler2D u_image;
  varying vec2 v_texCoord;

  void main() {
    gl_FragColor = texture2D(u_image, v_texCoord);
  }
`;

/**
 * 创建着色器程序
 */
function createShaderProgram(
	gl: WebGLRenderingContext,
	vertexShaderSource: string,
	fragmentShaderSource: string
) {
	// 创建顶点着色器
	const vertexShader = gl.createShader(gl.VERTEX_SHADER);
	if (!vertexShader) {
		throw new Error("无法创建顶点着色器");
	}
	gl.shaderSource(vertexShader, vertexShaderSource);
	gl.compileShader(vertexShader);

	// 检查顶点着色器编译状态
	if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
		const info = gl.getShaderInfoLog(vertexShader);
		gl.deleteShader(vertexShader);
		throw new Error("顶点着色器编译失败: " + info);
	}

	// 创建片段着色器
	const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
	if (!fragmentShader) {
		throw new Error("无法创建片段着色器");
	}
	gl.shaderSource(fragmentShader, fragmentShaderSource);
	gl.compileShader(fragmentShader);

	// 检查片段着色器编译状态
	if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
		const info = gl.getShaderInfoLog(fragmentShader);
		gl.deleteShader(fragmentShader);
		gl.deleteShader(vertexShader);
		throw new Error("片段着色器编译失败: " + info);
	}

	// 创建程序
	const program = gl.createProgram();
	if (!program) {
		throw new Error("无法创建WebGL程序");
	}

	gl.attachShader(program, vertexShader);
	gl.attachShader(program, fragmentShader);
	gl.linkProgram(program);

	// 检查程序链接状态
	if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
		const info = gl.getProgramInfoLog(program);
		gl.deleteProgram(program);
		gl.deleteShader(vertexShader);
		gl.deleteShader(fragmentShader);
		throw new Error("着色器程序链接失败: " + info);
	}

	return program;
}

/**
 * 创建WebGL渲染器
 */
export function createWebGLRenderer(canvas: HTMLCanvasElement) {
	// 获取WebGL上下文
	const gl = canvas.getContext("webgl", {
		preserveDrawingBuffer: true,
		antialias: false,
		alpha: false,
	});

	if (!gl) {
		throw new Error("无法创建WebGL上下文");
	}

	// 添加纹理缓存
	const textureCache = new Map<string, WebGLTexture>();
	let currentTexture: WebGLTexture | null = null;
	let lastBase64Data = "";

	// 创建着色器程序
	const program = createShaderProgram(gl, VERTEX_SHADER, FRAGMENT_SHADER);
	if (!program) {
		throw new Error("无法创建着色器程序");
	}

	// 获取属性位置
	const positionLocation = gl.getAttribLocation(program, "a_position");
	const texCoordLocation = gl.getAttribLocation(program, "a_texCoord");

	// 创建缓冲区
	const positionBuffer = gl.createBuffer();
	gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
	const positions = [-1, -1, 1, -1, -1, 1, 1, 1];
	gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

	// 创建纹理坐标缓冲区
	const texCoordBuffer = gl.createBuffer();
	gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
	const texCoords = [0, 1, 1, 1, 0, 0, 1, 0];
	gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(texCoords), gl.STATIC_DRAW);

	// 创建纹理
	const texture = gl.createTexture();
	gl.bindTexture(gl.TEXTURE_2D, texture);
	gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
	gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
	gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
	gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

	// 绘制场景函数
	const drawScene = () => {
		if (!gl || !currentTexture) return;

		// 清除画布
		gl.viewport(0, 0, canvas.width, canvas.height);
		gl.clearColor(0, 0, 0, 0);
		gl.clear(gl.COLOR_BUFFER_BIT);

		// 使用着色器程序
		gl.useProgram(program);

		// 设置位置属性
		gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
		gl.enableVertexAttribArray(positionLocation);
		gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

		// 设置纹理坐标属性
		gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
		gl.enableVertexAttribArray(texCoordLocation);
		gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 0, 0);

		// 绑定纹理
		gl.bindTexture(gl.TEXTURE_2D, currentTexture);

		// 绘制
		gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
	};

	// 修改updateTexture函数，支持缓存
	const updateTexture = (base64Data: string) => {
		// 如果数据相同，避免重复处理
		if (base64Data === lastBase64Data) return;

		lastBase64Data = base64Data;

		// 检查缓存中是否已有此纹理
		if (textureCache.has(base64Data)) {
			const cachedTexture = textureCache.get(base64Data);
			if (cachedTexture) {
				currentTexture = cachedTexture;
				// 使用缓存的纹理重绘
				drawScene();
				return;
			}
		}

		// 创建图像对象
		const image = new Image();

		// 图像加载完成后更新纹理
		image.onload = () => {
			if (!gl) return;

			// 调整Canvas尺寸以匹配图像
			if (
				canvas.width !== image.width ||
				canvas.height !== image.height
			) {
				canvas.width = image.width;
				canvas.height = image.height;
			}

			// 创建纹理
			const newTexture = gl.createTexture();
			if (!newTexture) return;

			// 绑定纹理
			gl.bindTexture(gl.TEXTURE_2D, newTexture);

			// 设置参数
			gl.texParameteri(
				gl.TEXTURE_2D,
				gl.TEXTURE_WRAP_S,
				gl.CLAMP_TO_EDGE
			);
			gl.texParameteri(
				gl.TEXTURE_2D,
				gl.TEXTURE_WRAP_T,
				gl.CLAMP_TO_EDGE
			);
			gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
			gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

			// 上传纹理
			gl.texImage2D(
				gl.TEXTURE_2D,
				0,
				gl.RGBA,
				gl.RGBA,
				gl.UNSIGNED_BYTE,
				image
			);

			// 保存当前纹理
			currentTexture = newTexture;

			// 将纹理添加到缓存
			if (textureCache.size > 2) {
				// 如果缓存过大，清除旧纹理
				const oldestKey = textureCache.keys().next().value;
				const oldTexture = textureCache.get(oldestKey);
				if (oldTexture) {
					gl.deleteTexture(oldTexture);
				}
				textureCache.delete(oldestKey);
			}

			// 保存新纹理到缓存
			textureCache.set(base64Data, newTexture);

			// 重绘场景
			drawScene();
		};

		// 设置图像源
		image.src = "data:image/jpg;base64," + base64Data;
	};

	// 添加清理纹理缓存方法
	const clearTextureCache = () => {
		if (!gl) return;

		// 删除所有缓存的纹理
		textureCache.forEach((texture) => {
			gl.deleteTexture(texture);
		});

		// 清空缓存Map
		textureCache.clear();

		// 重置当前纹理和上次数据引用
		currentTexture = null;
		lastBase64Data = "";
	};

	// 增强destroy方法，确保所有资源被释放
	const destroy = () => {
		if (!gl) return;

		// 清理纹理缓存
		clearTextureCache();

		// 删除着色器程序
		if (program) {
			gl.deleteProgram(program);
		}

		// 删除缓冲区
		gl.deleteBuffer(positionBuffer);
		gl.deleteBuffer(texCoordBuffer);

		// 不要尝试修改常量
		// 让GC来清理它们
	};

	return {
		updateTexture,
		clearTextureCache, // 导出清理方法
		destroy,
		getContext: () => gl,
	};
}
