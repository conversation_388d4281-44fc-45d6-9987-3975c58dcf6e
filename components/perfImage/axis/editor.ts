export class AxisDrawer {
	private canvas: HTMLCanvasElement;
	private ctx: CanvasRenderingContext2D;
	private axisColor: string;
	private axisWidth: number;
	private _isDragging: boolean = false;
	private selectedAxis: "x" | "y" | null = null;
	private currentX: number = 100;
	private currentY: number = 100;
	private xDirection: 1 | -1 = 1; // 1表示向右，-1表示向左
	private yDirection: 1 | -1 = 1; // 1表示向下，-1表示向上
	// 保存绑定后的事件处理函数引用
	private boundHandleMouseDown: (e: MouseEvent) => void;
	private boundHandleMouseMove: (e: MouseEvent) => void;
	private boundHandleMouseUp: (e: MouseEvent) => void;

	constructor(
		canvas: HTMLCanvasElement,
		axisColor: string = "#000000",
		axisWidth: number = 1
	) {
		this.canvas = canvas;
		this.ctx = canvas.getContext("2d", {
			willReadFrequently: true,
		})!;
		this.axisColor = axisColor;
		this.axisWidth = axisWidth;

		// 初始化绑定的事件处理函数
		this.boundHandleMouseDown = this.handleMouseDown.bind(this);
		this.boundHandleMouseMove = this.handleMouseMove.bind(this);
		this.boundHandleMouseUp = this.handleMouseUp.bind(this);
	}

	// 添加公共方法返回拖动状态
	isDragging(): boolean {
		return this._isDragging;
	}

	drawAxis() {
		const { width, height } = this.canvas;
		const centerX = width / 2;
		const centerY = height / 2;

		this.ctx.save();
		this.ctx.strokeStyle = this.axisColor;
		this.ctx.lineWidth = this.axisWidth;

		// 绘制X轴
		this.ctx.beginPath();
		this.ctx.moveTo(0, centerY);
		this.ctx.lineTo(width, centerY);
		this.ctx.stroke();

		// 绘制Y轴
		this.ctx.beginPath();
		this.ctx.moveTo(centerX, 0);
		this.ctx.lineTo(centerX, height);
		this.ctx.stroke();

		this.ctx.restore();
	}

	drawCrossAxis() {
		const { width, height } = this.canvas;
		const centerX = width / 2;
		const centerY = height / 2;

		this.ctx.save();
		this.ctx.strokeStyle = this.axisColor;
		this.ctx.lineWidth = this.axisWidth;

		// 旋转45度绘制十字轴
		this.ctx.translate(centerX, centerY);
		this.ctx.rotate(Math.PI / 4); // 旋转45度

		// 绘制X轴
		this.ctx.beginPath();
		this.ctx.moveTo(-width / 2, 0);
		this.ctx.lineTo(width / 2, 0);
		this.ctx.stroke();

		// 绘制Y轴
		this.ctx.beginPath();
		this.ctx.moveTo(0, -height / 2);
		this.ctx.lineTo(0, height / 2);
		this.ctx.stroke();

		this.ctx.restore();
	}

	clear() {
		// 清除画布
		this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
	}

	destroy() {
		// 清除画布
		this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

		// 移除事件监听，使用已保存的绑定函数引用
		this.canvas.removeEventListener("mousedown", this.boundHandleMouseDown);
		this.canvas.removeEventListener("mousemove", this.boundHandleMouseMove);
		this.canvas.removeEventListener("mouseup", this.boundHandleMouseUp);

		// 重置所有属性
		this.canvas = null!;
		this.ctx = null!;
		this.axisColor = "";
		this.axisWidth = 0;
	}

	drawRightAngle(xLength: number = 100, yLength: number = 100) {
		this.currentX = xLength;
		this.currentY = yLength;

		const { width, height } = this.canvas;
		const centerX = width / 2;
		const centerY = height / 2;

		this.ctx.save();
		this.ctx.strokeStyle = this.axisColor;
		this.ctx.lineWidth = this.axisWidth;

		// 根据方向绘制X轴
		this.ctx.beginPath();
		this.ctx.moveTo(centerX, centerY);
		this.ctx.lineTo(centerX + this.currentX * this.xDirection, centerY);
		this.ctx.stroke();

		// 根据方向绘制Y轴
		this.ctx.beginPath();
		this.ctx.moveTo(centerX, centerY);
		this.ctx.lineTo(centerX, centerY + this.currentY * this.yDirection);
		this.ctx.stroke();

		// 根据方向绘制控制点
		this.drawControlPoint(
			centerX + this.currentX * this.xDirection,
			centerY
		);
		this.drawControlPoint(
			centerX,
			centerY + this.currentY * this.yDirection
		);

		this.ctx.restore();
	}

	// 绘制控制点
	private drawControlPoint(x: number, y: number) {
		this.ctx.beginPath();
		this.ctx.fillStyle = this.axisColor;
		this.ctx.arc(x, y, 4, 0, Math.PI * 2);
		this.ctx.fill();

		// 绘制控制点的外圈
		this.ctx.beginPath();
		this.ctx.strokeStyle = "#ffffff";
		this.ctx.lineWidth = 2;
		this.ctx.arc(x, y, 5, 0, Math.PI * 2);
		this.ctx.stroke();
	}

	// 检查鼠标是否在控制点上
	private isOnControlPoint(
		mouseX: number,
		mouseY: number,
		controlX: number,
		controlY: number
	): boolean {
		const distance = Math.sqrt(
			Math.pow(mouseX - controlX, 2) + Math.pow(mouseY - controlY, 2)
		);
		return distance <= 8; // 控制点的可点击范围
	}

	// 添加事件监听
	initDragEvents() {
		// 使用已绑定的函数引用
		this.canvas.addEventListener("mousedown", this.boundHandleMouseDown);
		this.canvas.addEventListener("mousemove", this.boundHandleMouseMove);
		this.canvas.addEventListener("mouseup", this.boundHandleMouseUp);
	}

	private handleMouseDown(e: MouseEvent) {
		const { width, height } = this.canvas;
		const centerX = width / 2;
		const centerY = height / 2;

		const mouseX = e.offsetX;
		const mouseY = e.offsetY;

		// 检查点击位置并设置方向
		if (
			this.isOnControlPoint(
				mouseX,
				mouseY,
				centerX + this.currentX * this.xDirection,
				centerY
			)
		) {
			this._isDragging = true;
			this.selectedAxis = "x";
			this.xDirection = mouseX < centerX ? -1 : 1;
			this.canvas.style.cursor = "ew-resize";
		} else if (
			this.isOnControlPoint(
				mouseX,
				mouseY,
				centerX,
				centerY + this.currentY * this.yDirection
			)
		) {
			this._isDragging = true;
			this.selectedAxis = "y";
			this.yDirection = mouseY < centerY ? -1 : 1;
			this.canvas.style.cursor = "ns-resize";
		}
	}

	private handleMouseMove(e: MouseEvent) {
		const { width, height } = this.canvas;
		const centerX = width / 2;
		const centerY = height / 2;

		// 更新鼠标样式
		if (!this._isDragging) {
			if (
				this.isOnControlPoint(
					e.offsetX,
					e.offsetY,
					centerX + this.currentX,
					centerY
				)
			) {
				this.canvas.style.cursor = "ew-resize";
			} else if (
				this.isOnControlPoint(
					e.offsetX,
					e.offsetY,
					centerX,
					centerY + this.currentY
				)
			) {
				this.canvas.style.cursor = "ns-resize";
			} else {
				this.canvas.style.cursor = "default";
			}
			return;
		}

		// 处理拖拽
		if (this.selectedAxis === "x") {
			const newLength = Math.abs(e.offsetX - centerX);
			this.xDirection = e.offsetX < centerX ? -1 : 1;
			this.clear();
			this.drawRightAngle(newLength, this.currentY);
		} else if (this.selectedAxis === "y") {
			const newLength = Math.abs(e.offsetY - centerY);
			this.yDirection = e.offsetY < centerY ? -1 : 1;
			this.clear();
			this.drawRightAngle(this.currentX, newLength);
		}
	}

	private handleMouseUp() {
		this._isDragging = false;
		this.selectedAxis = null;
		this.canvas.style.cursor = "default";
	}
}
