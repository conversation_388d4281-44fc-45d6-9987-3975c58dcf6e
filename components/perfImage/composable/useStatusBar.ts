import { ref, Ref } from "vue";
import { ScreenMode } from "../type";
import { useI18n } from "vue-i18n";

export function useStatusBar(screenMode: Ref<ScreenMode>) {
	const { t } = useI18n();

	// 状态栏相关
	const statuses = ref([
		{ type: "real-time", icon: "icon-shexiangji", label: "实时" },
		{ type: "inner-video", icon: "icon-video", label: "内触发" },
		{
			type: "video-off",
			icon: "icon-shexiangtou_guanbi",
			label: "关闭实时/内触发",
		},
		{ type: "tiles", icon: "icon-image-fill", label: "图片" },
		{ type: "image", icon: "icon-camera", label: "拍照" },
	]);

	const handleStatusClick = async (status: {
		type: string;
		icon: string;
		label: string;
	}) => {
		screenMode.value = status.type as typeof screenMode.value;
	};

	return {
		statuses,
		handleStatusClick,
	};
}
