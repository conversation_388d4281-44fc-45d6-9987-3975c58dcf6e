import { Tile } from "../type";

export const useTileCache = () => {
	const tileCache = new Map<string, Tile>();

	// 清理缓存
	const cleanupTileCache = (visibleTiles: Tile[]) => {
		const visibleTileIds = new Set(visibleTiles.map((tile) => tile.id));

		// 保留可见瓦片的缓存和相邻瓦片的缓存
		for (const [id] of tileCache) {
			if (!visibleTileIds.has(id)) {
				tileCache.delete(id);
			}
		}

		// 限制缓存大小
		if (tileCache.size > 100) {
			// 设置最大缓存数量
			const idsToDelete = Array.from(tileCache.keys()).slice(
				0,
				tileCache.size - 100
			);
			idsToDelete.forEach((id) => tileCache.delete(id));
		}
	};

	return {
		tileCache,
		cleanupTileCache,
	};
};
