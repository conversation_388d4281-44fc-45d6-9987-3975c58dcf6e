import { nextTick, ref, Ref } from "vue";
import { AxisDrawer } from "../axis/editor";
import { ScreenMode } from "../type";
import { createWebGLRenderer } from "../render/webgl-renderer";
import { throttle } from "lodash";

export const useRealTimeMode = (
	props: {
		imageUrl: string;
	},
	screenMode: Ref<ScreenMode>,
	emit: (
		event: "move" | "send" | "videoOn" | "videoOff" | "photo" | "post",
		...args: any[]
	) => void,
	container: Ref<HTMLElement | null>,
	useWebGL: Ref<boolean>
) => {
	const axisRef = ref<AxisDrawer | null>(null); // 轴线
	const imageContainer = ref<HTMLElement | null>(null);
	const imageCanvas = ref<HTMLCanvasElement | null>(null);
	const imageDisplayCanvas = ref<HTMLCanvasElement | null>(null);
	const imageScale = ref(1); // 缩放状态
	const preventNextClick = ref(false); // 是否阻止下一次点击事件
	// 轴线绘制缓存
	const cachedAxis = {
		mainAxis: null as HTMLCanvasElement | null,
		crossAxis: null as HTMLCanvasElement | null,
		rightAngle: null as HTMLCanvasElement | null,

		// 清除所有缓存
		clear() {
			this.mainAxis = null;
			this.crossAxis = null;
			this.rightAngle = null;
		},

		// 创建缓存
		create(type: string, width: number, height: number) {
			const canvas = document.createElement("canvas");
			canvas.width = width;
			canvas.height = height;
			this[type as keyof typeof this] = canvas;
			return canvas;
		},
	};

	// 图像对象池
	const imagePool = {
		busy: new Set<HTMLImageElement>(),
		free: [] as HTMLImageElement[],
		get() {
			let img = this.free.pop() || new Image();
			this.busy.add(img);
			return img;
		},
		release(img: HTMLImageElement) {
			if (!this.busy.has(img)) return;
			img.onload = null;
			img.onerror = null;
			img.src = "";
			this.busy.delete(img);
			this.free.push(img);
		},
		clear() {
			this.free = [];
			this.busy.clear();
		},
	};

	const lastImageUrl = ref(""); // 上次图像URL，用于避免重复处理
	const animationFrameId = ref<number | null>(null); // requestAnimationFrame ID
	const realTimeGLRenderer = ref<ReturnType<
		typeof createWebGLRenderer
	> | null>(null);
	const resizeObserver = ref<ResizeObserver | null>(null); // 尺寸监听
	const canvasResizeThrottled = ref<boolean>(false); // 防止频繁调整Canvas尺寸

	const MAX_FPS = 240; // 最大帧率提高到240
	const MIN_FRAME_TIME = 1000 / MAX_FPS; // 最小帧间隔时间(ms)
	const lastFrameTime = ref(0); // 上次渲染帧的时间戳
	const imageProcessQueue = ref<string[]>([]); // 图像处理队列
	const maxQueueSize = 30; // 最大队列大小 (提高到30以处理更多帧)
	const isProcessingQueue = ref(false); // 是否正在处理队列

	const showAxis = ref(false);
	const currentRealTimeTool = ref<string | null>(null);
	const realTimeTools = ref([
		{ type: "mainAxis", icon: "icon-tianjia2", label: "主轴" },
		{ type: "crossAxis", icon: "icon-tianjia2-copy", label: "交叉轴" },
		{ type: "rightAngle", icon: "icon-neizhijiaox", label: "直角" },
		{ type: "clear", icon: "icon-qingchu", label: "清除" },
	]);

	const handleRealTimeToolClick = (tool: { type: string; icon: string }) => {
		if (!axisRef.value) return;
		currentRealTimeTool.value = tool.type;

		if (tool.type === "clear") {
			axisRef.value.clear();
			currentRealTimeTool.value = null;
			showAxis.value = false;
			cachedAxis.clear(); // 清除缓存
		} else {
			renderAxisByType(tool.type);
			showAxis.value = true;
		}
	};

	// 实时模式初始化
	const initImageCanvas = () => {
		if (!imageCanvas.value || !imageDisplayCanvas.value || !container.value)
			return;

		// 确保先清理旧的实例
		if (axisRef.value) {
			axisRef.value.destroy();
			axisRef.value = null;
		}

		// 重置渲染状态
		cleanupImageRendering();

		// 清理ResizeObserver
		if (resizeObserver.value) {
			resizeObserver.value.disconnect();
			resizeObserver.value = null;
		}

		nextTick(() => {
			if (
				!imageCanvas.value ||
				!imageDisplayCanvas.value ||
				!imageContainer.value
			)
				return;

			// 建立ResizeObserver监听容器尺寸变化
			resizeObserver.value = new ResizeObserver(
				throttle(
					() => {
						if (
							!imageDisplayCanvas.value ||
							!imageCanvas.value ||
							!container.value ||
							canvasResizeThrottled.value
						)
							return;

						canvasResizeThrottled.value = true;

						requestAnimationFrame(() => {
							// 保存当前图像数据，以便在调整大小后恢复
							let imgData: ImageData | null = null;

							if (imageDisplayCanvas.value && !useWebGL.value) {
								const ctx = imageDisplayCanvas.value.getContext(
									"2d",
									{ willReadFrequently: true }
								);
								if (ctx) {
									try {
										imgData = ctx.getImageData(
											0,
											0,
											imageDisplayCanvas.value.width,
											imageDisplayCanvas.value.height
										);
									} catch (e) {
										console.error(
											"Failed to get image data",
											e
										);
									}
								}
							}

							const containerRect =
								container.value!.getBoundingClientRect();
							const width = Math.round(containerRect.width);
							const height = Math.round(containerRect.height);

							// 仅在尺寸有明显变化时才调整Canvas
							if (
								Math.abs(
									imageDisplayCanvas.value!.width - width
								) > 10 ||
								Math.abs(
									imageDisplayCanvas.value!.height - height
								) > 10
							) {
								imageDisplayCanvas.value!.width = width;
								imageDisplayCanvas.value!.height = height;
								imageCanvas.value!.width = width;
								imageCanvas.value!.height = height;

								// 重新初始化轴线绘制器
								if (axisRef.value) {
									axisRef.value.destroy();
								}
								axisRef.value = new AxisDrawer(
									imageCanvas.value!,
									"red",
									1
								);

								// 清除轴线缓存
								cachedAxis.clear();

								// 恢复之前的轴线状态
								if (
									showAxis.value &&
									currentRealTimeTool.value
								) {
									renderAxisByType(currentRealTimeTool.value);
								}

								// 重新渲染图像（如果有）
								if (imgData && !useWebGL.value) {
									const ctx =
										imageDisplayCanvas.value!.getContext(
											"2d",
											{ willReadFrequently: true }
										);
									if (ctx) {
										try {
											// 缩放图像数据以适应新尺寸
											if (
												imgData.width > 0 &&
												imgData.height > 0
											) {
												ctx.putImageData(imgData, 0, 0);
											}
										} catch (e) {
											// 如果图像放置失败，重新渲染
											if (lastImageUrl.value) {
												queueImageData(
													lastImageUrl.value
												);
											}
										}
									}
								} else if (lastImageUrl.value) {
									if (
										useWebGL.value &&
										realTimeGLRenderer.value
									) {
										realTimeGLRenderer.value.updateTexture(
											lastImageUrl.value
										);
									} else {
										queueImageData(lastImageUrl.value);
									}
								}
							}

							canvasResizeThrottled.value = false;
						});
					},
					100,
					{ leading: true, trailing: true }
				)
			);

			if (container.value) {
				resizeObserver.value.observe(container.value);
			}

			// 初始化Canvas尺寸
			if (container.value) {
				const containerRect = container.value.getBoundingClientRect();
				imageDisplayCanvas.value.width = containerRect.width;
				imageDisplayCanvas.value.height = containerRect.height;
				imageCanvas.value.width = containerRect.width;
				imageCanvas.value.height = containerRect.height;

				// 初始化WebGL渲染器
				if (useWebGL.value) {
					try {
						realTimeGLRenderer.value = createWebGLRenderer(
							imageDisplayCanvas.value
						);

						// 如果已有图像数据，立即渲染
						if (
							props.imageUrl &&
							props.imageUrl !== lastImageUrl.value
						) {
							lastImageUrl.value = props.imageUrl;
							realTimeGLRenderer.value.updateTexture(
								props.imageUrl
							);
						}
					} catch (err) {
						console.error(
							"WebGL初始化失败，回退到Canvas模式:",
							err
						);
						useWebGL.value = false;

						// 回退到Canvas渲染
						if (
							props.imageUrl &&
							props.imageUrl !== lastImageUrl.value
						) {
							lastImageUrl.value = props.imageUrl;
							queueImageData(props.imageUrl);
						}
					}
				} else {
					// 标准Canvas模式
					if (
						props.imageUrl &&
						props.imageUrl !== lastImageUrl.value
					) {
						lastImageUrl.value = props.imageUrl;
						queueImageData(props.imageUrl);
					}
				}
			}

			axisRef.value = new AxisDrawer(imageCanvas.value, "red", 1);
		});
	};

	// 添加图像队列管理函数
	const queueImageData = (imageData: string) => {
		// 当队列过长时，使用优化的丢帧策略，保留最新和关键帧
		if (imageProcessQueue.value.length >= maxQueueSize) {
			// 优化丢帧策略：保留队列头、队列尾和均匀分布的几个关键帧
			const newQueue: string[] = [];

			// 保留队列头部的第一帧
			newQueue.push(imageProcessQueue.value[0]);

			// 在中间均匀采样几个关键帧
			const middleFrames = 3; // 保留的中间帧数量
			const step = Math.floor(
				(imageProcessQueue.value.length - 2) / (middleFrames + 1)
			);

			if (step > 1) {
				for (let i = 1; i <= middleFrames; i++) {
					const index = Math.min(
						i * step,
						imageProcessQueue.value.length - 2
					);
					newQueue.push(imageProcessQueue.value[index]);
				}
			}

			// 加入最新的一帧
			newQueue.push(imageData);

			imageProcessQueue.value = newQueue;
		} else {
			imageProcessQueue.value.push(imageData);
		}

		// 如果当前没有处理队列，开始处理
		if (!isProcessingQueue.value) {
			processNextQueuedImage();
		}
	};

	// 处理队列中的下一个图像
	const processNextQueuedImage = () => {
		// 如果队列为空，重置处理状态
		if (imageProcessQueue.value.length === 0) {
			isProcessingQueue.value = false;
			return;
		}

		// 标记正在处理队列
		isProcessingQueue.value = true;

		// 获取当前时间
		const now = performance.now();

		// 检查是否需要跳过中间帧以赶上最新帧
		const shouldSkipToLatest =
			imageProcessQueue.value.length > maxQueueSize / 2;

		// 如果队列较长且与上一帧间隔足够长，可以考虑跳到最新帧
		if (
			shouldSkipToLatest &&
			now - lastFrameTime.value >= MIN_FRAME_TIME * 3
		) {
			// 保留第一帧用于连续性，丢弃中间所有帧，直接处理最新帧
			const firstFrame = imageProcessQueue.value.shift();
			const latestFrame = imageProcessQueue.value.pop();

			// 清空队列
			imageProcessQueue.value = [];

			// 如果有最新帧，渲染它；否则回退到第一帧
			if (latestFrame) {
				renderImageToCanvas(latestFrame);
			} else if (firstFrame) {
				renderImageToCanvas(firstFrame);
			} else {
				isProcessingQueue.value = false;
			}
			return;
		}

		// 正常模式：如果与上一帧间隔足够长，处理下一张图片
		if (now - lastFrameTime.value >= MIN_FRAME_TIME) {
			const nextImageData = imageProcessQueue.value.shift();
			if (nextImageData) {
				renderImageToCanvas(nextImageData);
			}
		} else {
			// 如果间隔太短，延迟处理
			const timeToWait = MIN_FRAME_TIME - (now - lastFrameTime.value);
			setTimeout(() => {
				if (imageProcessQueue.value.length > 0) {
					const nextImageData = imageProcessQueue.value.shift();
					if (nextImageData) {
						renderImageToCanvas(nextImageData);
					}
				} else {
					isProcessingQueue.value = false;
				}
			}, timeToWait);
		}
	};

	// 渲染图像到Canvas
	const renderImageToCanvas = (imageData: string) => {
		// 立即重置animationFrameId，以便能继续处理后续帧
		animationFrameId.value = null;

		// 如果不是实时模式或组件已卸载，不进行渲染
		if (!imageDisplayCanvas.value || screenMode.value !== "real-time") {
			// 在无法渲染的情况下，仍尝试处理队列
			setTimeout(processNextQueuedImage, 0);
			return;
		}

		// 更新上次渲染时间戳
		lastFrameTime.value = performance.now();

		// 使用对象池获取图像实例
		const img = imagePool.get();

		// 优化图像加载流程
		const loadHandler = () => {
			if (!imageDisplayCanvas.value) {
				imagePool.release(img);
				// 继续处理队列
				setTimeout(processNextQueuedImage, 0);
				return;
			}

			// 避免不必要的canvas尺寸调整，只在有显著变化时调整
			if (
				Math.abs(imageDisplayCanvas.value.width - img.width) > 20 ||
				Math.abs(imageDisplayCanvas.value.height - img.height) > 20
			) {
				imageDisplayCanvas.value.width = img.width;
				imageDisplayCanvas.value.height = img.height;

				// 同时调整绘图canvas尺寸
				if (imageCanvas.value) {
					imageCanvas.value.width = img.width;
					imageCanvas.value.height = img.height;
				}

				// 重新初始化轴线绘制器
				if (imageCanvas.value && axisRef.value) {
					axisRef.value.destroy();
					axisRef.value = new AxisDrawer(imageCanvas.value, "red", 1);

					// 清除轴线缓存
					cachedAxis.clear();

					// 恢复之前的轴线状态
					if (showAxis.value && currentRealTimeTool.value) {
						renderAxisByType(currentRealTimeTool.value);
					}
				}
			}

			// 立即绘制到Canvas，不使用requestAnimationFrame以减少延迟
			if (imageDisplayCanvas.value) {
				const ctx = imageDisplayCanvas.value.getContext("2d", {
					willReadFrequently: true,
					alpha: false, // 禁用alpha通道以提高性能
				});

				if (ctx) {
					// 使用更高效的清除方法
					ctx.fillStyle = "#000";
					ctx.fillRect(
						0,
						0,
						imageDisplayCanvas.value.width,
						imageDisplayCanvas.value.height
					);

					// 绘制图像
					ctx.drawImage(img, 0, 0);
				}
			}

			// 释放图像回对象池
			imagePool.release(img);

			// 处理队列中的下一帧
			processNextQueuedImage();
		};

		const errorHandler = (err: any) => {
			console.error("图像加载失败:", err);
			imagePool.release(img);

			// 错误时也尝试处理队列
			processNextQueuedImage();
		};

		// 设置图像事件处理器
		img.onload = loadHandler;
		img.onerror = errorHandler;

		// 设置图像源
		img.src = "data:image/jpg;base64," + imageData;
	};

	// 渲染轴线
	const renderAxisByType = (type: string) => {
		if (!axisRef.value) return;

		if (type === "rightAngle") {
			axisRef.value.clear();
			axisRef.value.initDragEvents();
			axisRef.value.drawRightAngle();
		} else if (type === "crossAxis") {
			axisRef.value.clear();
			axisRef.value.drawCrossAxis();
		} else if (type === "mainAxis") {
			axisRef.value.clear();
			axisRef.value.drawAxis();
		}
	};

	// 实时模式 缩放
	const handleImageWheel = throttle(
		(e: WheelEvent) => {
			if (!imageContainer.value || screenMode.value !== "real-time")
				return;

			e.preventDefault();

			const delta = e.deltaY > 0 ? -0.1 : 0.1;
			const newScale = Math.min(Math.max(imageScale.value + delta, 1), 5);

			if (newScale !== imageScale.value) {
				imageScale.value = newScale;

				if (imageContainer.value) {
					const contentWrapper = imageContainer.value.querySelector(
						".content-wrapper"
					) as HTMLElement;
					if (contentWrapper) {
						// 使用transform: scale代替重绘
						contentWrapper.style.transform = `scale(${newScale})`;
					}
				}
			}
		},
		50,
		{ leading: true, trailing: true }
	);

	// 处理图像Canvas的mousedown事件区分锚点控制和像素移动
	const handleImageMouseDown = (e: MouseEvent) => {
		// 如果是直角工具模式且点击了控制点，则阻止事件冒泡防止触发click
		if (
			currentRealTimeTool.value === "rightAngle" &&
			axisRef.value &&
			imageCanvas.value
		) {
			// 如果鼠标样式是ew-resize或ns-resize，表示鼠标在控制点上
			const computedStyle = window.getComputedStyle(imageCanvas.value);
			if (
				computedStyle.cursor === "ew-resize" ||
				computedStyle.cursor === "ns-resize"
			) {
				// 阻止事件冒泡
				e.stopPropagation();
				// 设置标记，阻止下一次点击事件
				preventNextClick.value = true;

				// 添加一次性的mouseup事件，在抬起鼠标后一段时间内重置preventNextClick
				const cleanup = () => {
					// 延迟重置标记，确保click事件已经处理完
					setTimeout(() => {
						preventNextClick.value = false;
					}, 100);
					document.removeEventListener("mouseup", cleanup);
				};

				document.addEventListener("mouseup", cleanup);
			}
		}
	};

	// 实时模式 像素移动
	const handleImageCanvasClick = (e: MouseEvent) => {
		if (!imageCanvas.value || !showAxis.value) return;

		// 检查是否应该阻止点击事件
		if (
			preventNextClick.value ||
			(currentRealTimeTool.value === "rightAngle" &&
				axisRef.value &&
				axisRef.value.isDragging())
		) {
			return;
		}

		// 直接计算坐标并发送，不使用requestAnimationFrame
		const rect = imageCanvas.value.getBoundingClientRect();
		const centerX = rect.width / 2;
		const centerY = rect.height / 2;

		// 考虑缩放因素
		const x = (e.clientX - rect.left - centerX) / imageScale.value;
		const y = (centerY - (e.clientY - rect.top)) / imageScale.value;

		emit("move", x, y);
	};

	// 清理资源
	const cleanupImageRendering = () => {
		if (animationFrameId.value !== null) {
			cancelAnimationFrame(animationFrameId.value);
			animationFrameId.value = null;
		}

		// 清理WebGL资源
		if (realTimeGLRenderer.value) {
			// 确保删除所有纹理
			realTimeGLRenderer.value.destroy();
			realTimeGLRenderer.value = null;
		}

		// 清理队列和处理状态
		imageProcessQueue.value = [];
		isProcessingQueue.value = false;

		lastImageUrl.value = "";
	};

	return {
		axisRef,
		imageContainer,
		imageCanvas,
		imageDisplayCanvas,
		imageScale,
		cachedAxis,
		imagePool,
		lastImageUrl,
		realTimeGLRenderer,
		resizeObserver,
		imageProcessQueue,
		isProcessingQueue,
		showAxis,
		currentRealTimeTool,
		realTimeTools,
		handleRealTimeToolClick,
		initImageCanvas,
		queueImageData,
		handleImageWheel,
		handleImageMouseDown,
		handleImageCanvasClick,
		cleanupImageRendering,
	};
};
