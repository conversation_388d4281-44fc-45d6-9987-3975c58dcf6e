import { nextTick, ref, Ref } from "vue";
import { ScreenMode } from "../type";
import { createWebGLRenderer } from "../render/webgl-renderer";
import { throttle } from "lodash";

export const useInnerVideoMode = (
	props: {
		imageUrl: string;
	},
	screenMode: Ref<ScreenMode>,
	useWebGL: Ref<boolean>
) => {
	const innerVideoContainer = ref<HTMLElement | null>(null);
	const innerVideoCanvas = ref<HTMLCanvasElement | null>(null);
	const lastInnerVideoUrl = ref(""); // 上次内触发图像URL
	const innerVideoFrameId = ref<number | null>(null); // 内触发模式的requestAnimationFrame ID
	const innerVideoGLRenderer = ref<ReturnType<
		typeof createWebGLRenderer
	> | null>(null);
	const innerVideoResizeObserver = ref<ResizeObserver | null>(null); // 内触发模式尺寸监听器
	const innerVideoResizeThrottled = ref<boolean>(false); // 防止频繁调整Canvas尺寸
	const innerVideoQueue = ref<string[]>([]);
	const isProcessingInnerVideoQueue = ref(false);
	const MAX_FPS = 60; // 新增：最大帧率设置为60fps
	const MIN_FRAME_TIME = 1000 / MAX_FPS; // 最小帧间隔时间修改为对应最大帧率
	const lastFrameTime = ref(0); // 上次渲染帧的时间戳
	const maxQueueSize = 10; // 最大队列大小提高到10

	const innerVideoImagePool = {
		busy: new Set<HTMLImageElement>(),
		free: [] as HTMLImageElement[],
		get() {
			let img = this.free.pop() || new Image();
			this.busy.add(img);
			return img;
		},
		release(img: HTMLImageElement) {
			if (!this.busy.has(img)) return;
			img.onload = null;
			img.onerror = null;
			img.src = "";
			this.busy.delete(img);
			this.free.push(img);
		},
		clear() {
			this.free = [];
			this.busy.clear();
		},
	};

	// 内触发模式初始化
	const initInnerVideo = () => {
		if (!innerVideoCanvas.value || !innerVideoContainer.value) return;

		// 清理旧的资源
		cleanupInnerVideoRendering();

		// 清理ResizeObserver
		if (innerVideoResizeObserver.value) {
			innerVideoResizeObserver.value.disconnect();
			innerVideoResizeObserver.value = null;
		}

		nextTick(() => {
			if (!innerVideoCanvas.value || !innerVideoContainer.value) return;

			// 建立ResizeObserver监听容器尺寸变化
			innerVideoResizeObserver.value = new ResizeObserver(
				throttle(
					() => {
						if (
							!innerVideoCanvas.value ||
							!innerVideoContainer.value ||
							innerVideoResizeThrottled.value
						)
							return;

						innerVideoResizeThrottled.value = true;

						requestAnimationFrame(() => {
							// 保存当前图像数据，以便在调整大小后恢复
							let imgData: ImageData | null = null;

							if (innerVideoCanvas.value && !useWebGL.value) {
								const ctx = innerVideoCanvas.value.getContext(
									"2d",
									{
										willReadFrequently: true,
									}
								);
								if (ctx) {
									try {
										imgData = ctx.getImageData(
											0,
											0,
											innerVideoCanvas.value.width,
											innerVideoCanvas.value.height
										);
									} catch (e) {
										console.error(
											"Failed to get inner video image data",
											e
										);
									}
								}
							}

							const containerRect =
								innerVideoContainer.value!.getBoundingClientRect();
							const width = Math.round(containerRect.width);
							const height = Math.round(containerRect.height);

							// 仅在尺寸有明显变化时才调整Canvas
							if (
								Math.abs(
									innerVideoCanvas.value!.width - width
								) > 10 ||
								Math.abs(
									innerVideoCanvas.value!.height - height
								) > 10
							) {
								innerVideoCanvas.value!.width = width;
								innerVideoCanvas.value!.height = height;

								// 重新渲染图像（如果有）
								if (imgData && !useWebGL.value) {
									const ctx =
										innerVideoCanvas.value!.getContext(
											"2d"
										);
									if (ctx) {
										try {
											// 尝试恢复图像数据
											if (
												imgData.width > 0 &&
												imgData.height > 0
											) {
												ctx.putImageData(imgData, 0, 0);
											}
										} catch (e) {
											// 如果图像放置失败，重新渲染
											if (lastInnerVideoUrl.value) {
												renderInnerVideoToCanvas(
													lastInnerVideoUrl.value
												);
											}
										}
									}
								} else if (lastInnerVideoUrl.value) {
									if (
										useWebGL.value &&
										innerVideoGLRenderer.value
									) {
										innerVideoGLRenderer.value.updateTexture(
											lastInnerVideoUrl.value
										);
									} else {
										renderInnerVideoToCanvas(
											lastInnerVideoUrl.value
										);
									}
								}
							}

							innerVideoResizeThrottled.value = false;
						});
					},
					100,
					{ leading: true, trailing: true }
				)
			);

			// 确保容器存在再添加观察
			if (innerVideoContainer.value) {
				innerVideoResizeObserver.value.observe(
					innerVideoContainer.value
				);
			}

			// 初始化Canvas尺寸
			const containerRect =
				innerVideoContainer.value.getBoundingClientRect();
			innerVideoCanvas.value.width = containerRect.width;
			innerVideoCanvas.value.height = containerRect.height;

			// 初始化WebGL渲染器
			if (useWebGL.value) {
				try {
					innerVideoGLRenderer.value = createWebGLRenderer(
						innerVideoCanvas.value
					);

					// 如果已有图像数据，立即渲染
					if (
						props.imageUrl &&
						props.imageUrl !== lastInnerVideoUrl.value
					) {
						lastInnerVideoUrl.value = props.imageUrl;
						innerVideoGLRenderer.value.updateTexture(
							props.imageUrl
						);
					}
				} catch (err) {
					console.error("WebGL初始化失败，回退到Canvas模式:", err);
					useWebGL.value = false;

					// 回退到Canvas渲染
					if (
						props.imageUrl &&
						props.imageUrl !== lastInnerVideoUrl.value
					) {
						lastInnerVideoUrl.value = props.imageUrl;
						renderInnerVideoToCanvas(props.imageUrl);
					}
				}
			} else {
				// 标准Canvas模式
				if (
					props.imageUrl &&
					props.imageUrl !== lastInnerVideoUrl.value
				) {
					lastInnerVideoUrl.value = props.imageUrl;
					renderInnerVideoToCanvas(props.imageUrl);
				}
			}
		});
	};

	// 渲染内触发图像到Canvas
	const renderInnerVideoToCanvas = (imageData: string) => {
		if (innerVideoFrameId.value !== null) {
			cancelAnimationFrame(innerVideoFrameId.value);
			innerVideoFrameId.value = null;
		}

		// 如果不是内触发模式或组件已卸载，不进行渲染
		if (!innerVideoCanvas.value || screenMode.value !== "inner-video") {
			// 在无法渲染的情况下，仍尝试处理队列
			setTimeout(processNextInnerVideoFrame, 0);
			return;
		}

		// 更新上次渲染时间戳
		lastFrameTime.value = performance.now();

		// 使用对象池获取图像
		const img = innerVideoImagePool.get();

		// 优化图像加载处理
		const loadHandler = () => {
			if (!innerVideoCanvas.value) {
				innerVideoImagePool.release(img);
				// 继续处理队列
				setTimeout(processNextInnerVideoFrame, 0);
				return;
			}

			// 避免不必要的尺寸调整，只在有显著变化时调整
			if (
				Math.abs(innerVideoCanvas.value.width - img.width) > 20 ||
				Math.abs(innerVideoCanvas.value.height - img.height) > 20
			) {
				innerVideoCanvas.value.width = img.width;
				innerVideoCanvas.value.height = img.height;
			}

			// 立即绘制图像，不使用requestAnimationFrame以减少延迟
			const ctx = innerVideoCanvas.value.getContext("2d", {
				willReadFrequently: true,
				alpha: false, // 禁用alpha通道以提高性能
			});

			if (ctx) {
				// 使用更高效的清除方法
				ctx.fillStyle = "#000";
				ctx.fillRect(
					0,
					0,
					innerVideoCanvas.value.width,
					innerVideoCanvas.value.height
				);

				// 绘制图像
				ctx.drawImage(img, 0, 0);
			}

			// 绘制完成后释放图像回对象池
			innerVideoImagePool.release(img);

			// 处理队列中的下一帧
			processNextInnerVideoFrame();
		};

		const errorHandler = (err: any) => {
			console.error("内触发图像加载失败:", err);
			innerVideoImagePool.release(img);

			// 错误时也尝试处理队列
			processNextInnerVideoFrame();
		};

		// 设置图像事件处理器
		img.onload = loadHandler;
		img.onerror = errorHandler;

		// 设置图像源
		img.src = "data:image/jpg;base64," + imageData;
	};

	// 添加内触发模式图像队列管理函数
	const queueInnerVideoData = (imageData: string) => {
		// 当队列过长时，使用优化的丢帧策略
		if (innerVideoQueue.value.length >= maxQueueSize) {
			// 优化策略：保留首尾帧和关键中间帧
			const newQueue: string[] = [];

			// 保留第一帧
			if (innerVideoQueue.value.length > 0) {
				newQueue.push(innerVideoQueue.value[0]);
			}

			// 保留1-2个关键中间帧
			if (innerVideoQueue.value.length > 3) {
				// 选择距离中点最近的帧
				const midIndex = Math.floor(innerVideoQueue.value.length / 2);
				newQueue.push(innerVideoQueue.value[midIndex]);
			}

			// 添加最新帧
			newQueue.push(imageData);

			innerVideoQueue.value = newQueue;
		} else {
			innerVideoQueue.value.push(imageData);
		}

		// 如果当前没有处理队列，开始处理
		if (!isProcessingInnerVideoQueue.value) {
			processNextInnerVideoFrame();
		}
	};

	// 处理队列中的下一个内触发图像
	const processNextInnerVideoFrame = () => {
		// 如果队列为空，重置处理状态
		if (innerVideoQueue.value.length === 0) {
			isProcessingInnerVideoQueue.value = false;
			return;
		}

		// 标记正在处理队列
		isProcessingInnerVideoQueue.value = true;

		// 获取当前时间
		const now = performance.now();

		// 检查队列长度是否需要跳帧处理
		const shouldSkipFrames =
			innerVideoQueue.value.length > maxQueueSize / 2;

		// 如果队列较长，采用跳帧策略直接显示最新帧
		if (
			shouldSkipFrames &&
			now - lastFrameTime.value >= MIN_FRAME_TIME * 2
		) {
			// 保留最新的帧，丢弃其他所有帧
			const latestFrame = innerVideoQueue.value.pop();
			innerVideoQueue.value = [];

			if (latestFrame) {
				renderInnerVideoToCanvas(latestFrame);
			} else {
				isProcessingInnerVideoQueue.value = false;
			}
			return;
		}

		// 正常处理模式：如果与上一帧间隔足够长，处理下一张图片
		if (now - lastFrameTime.value >= MIN_FRAME_TIME) {
			const nextImageData = innerVideoQueue.value.shift();
			if (nextImageData) {
				renderInnerVideoToCanvas(nextImageData);
			}
		} else {
			// 如果间隔太短，延迟处理
			const timeToWait = MIN_FRAME_TIME - (now - lastFrameTime.value);
			setTimeout(() => {
				if (innerVideoQueue.value.length > 0) {
					const nextImageData = innerVideoQueue.value.shift();
					if (nextImageData) {
						renderInnerVideoToCanvas(nextImageData);
					}
				} else {
					isProcessingInnerVideoQueue.value = false;
				}
			}, timeToWait);
		}
	};

	// 清理内触发模式渲染资源
	const cleanupInnerVideoRendering = () => {
		if (innerVideoFrameId.value !== null) {
			cancelAnimationFrame(innerVideoFrameId.value);
			innerVideoFrameId.value = null;
		}

		// 清理WebGL资源
		if (innerVideoGLRenderer.value) {
			// 确保删除所有纹理并释放GPU资源
			innerVideoGLRenderer.value.destroy();
			innerVideoGLRenderer.value = null;
		}

		// 清理ResizeObserver
		if (innerVideoResizeObserver.value) {
			innerVideoResizeObserver.value.disconnect();
			innerVideoResizeObserver.value = null;
		}

		// 清理队列和处理状态
		innerVideoQueue.value = [];
		isProcessingInnerVideoQueue.value = false;

		lastInnerVideoUrl.value = "";
	};

	return {
		innerVideoImagePool,
		innerVideoContainer,
		innerVideoCanvas,
		lastInnerVideoUrl,
		innerVideoGLRenderer,
		innerVideoResizeObserver,
		innerVideoQueue,
		isProcessingInnerVideoQueue,
		initInnerVideo,
		queueInnerVideoData,
		cleanupInnerVideoRendering,
	};
};
