import { computed, ref, Ref, watch } from "vue";
import { MapDataType, ScreenMode } from "../type";
import { MapWorkerManager } from "../worker/mapworker-utils";
import { MapDataManager } from "../worker/map-utils";
import { throttle, debounce } from "lodash";
import { drawConfig } from "../config";

export const useMapMode = (
	props: {
		mapData?: MapDataType; // Map数据
		mapRatio: number; // Map 单元格比例（宽：高）
	},
	screenMode: Ref<ScreenMode>,
	emit: (
		event: "move" | "send" | "videoOn" | "videoOff" | "photo" | "post",
		...args: any[]
	) => void,
	container: Ref<HTMLElement | null>
) => {
	const mapContainer = ref<HTMLElement | null>(null);
	const mapLayerCanvas = ref<HTMLCanvasElement | null>(null);
	const mapDrawCanvas = ref<HTMLCanvasElement | null>(null);
	const mapLayerContext = ref<CanvasRenderingContext2D | null>(null);
	const mapDrawContext = ref<CanvasRenderingContext2D | null>(null);
	const mapScale = ref(1);
	const mapMinScale = ref(0.1); // 最小缩放比例
	const mapPosition = ref({ x: 0, y: 0 });
	const mapDragging = ref(false);
	const mapDragStartPosition = ref({ x: 0, y: 0 });
	const mapDragLastPosition = ref({ x: 0, y: 0 });
	const mapCellSize = ref(10);
	const mapIsDrawing = ref(false);
	const mapSelectionStart = ref<{ row: number; col: number } | null>(null);
	const mapSelectionCurrent = ref<{ row: number; col: number } | null>(null);
	const maps = ref<
		{
			row: number;
			col: number;
			id: string;
			fillColor?: string;
			strokeColor?: string;
		}[]
	>([]);
	const mapWorker = new MapWorkerManager(); // Map Worker 实例

	const mapRenderPending = ref(false);

	const showMapTooltip = ref(false); // Tooltip
	const mapTooltipText = ref("");
	const mapTooltipStyle = ref({
		left: "0px",
		top: "0px",
	});
	const mapHoverTimer = ref<number | null>(null);
	const mapHoverPosition = ref<{ row: number; col: number } | null>(null);
	const mapLayerCache = ref<Map<string, ImageData>>(new Map()); // 地图块缓存
	const lastMapRenderScale = ref(0); // 上次渲染的缩放比例
	const mapChunkSize = ref(100); // 分块大小，可以根据性能调整
	// 地图批处理相关数据
	const mapDataManager = new MapDataManager(mapWorker);
	// 记录已经加载的标记索引
	const loadedMapIndexes = new Set<number>();
	// 批量处理大小常量
	const BATCH_SIZE = 1000;
	// 标记地图模式
	const isMapMode = computed(() => screenMode.value === "video-off");
	// 地图加载相关状态
	const isMapDataProcessing = computed(
		() => mapDataManager.isProcessing.value
	);

	// 对象池用于管理 Map 标记对象
	const mapMarkPool = {
		free: [] as {
			row: number;
			col: number;
			id: string;
			fillColor?: string;
			strokeColor?: string;
		}[],

		get(
			row: number,
			col: number
		): {
			row: number;
			col: number;
			id: string;
			fillColor?: string;
			strokeColor?: string;
		} {
			if (this.free.length > 0) {
				const mark = this.free.pop()!;
				mark.row = row;
				mark.col = col;
				mark.id = Date.now().toString() + `-${row}-${col}`;
				return mark;
			}

			return {
				row,
				col,
				id: Date.now().toString() + `-${row}-${col}`,
			};
		},

		release(
			marks: {
				row: number;
				col: number;
				id: string;
				fillColor?: string;
				strokeColor?: string;
			}[]
		) {
			this.free.push(...marks);

			// 限制对象池大小，避免内存占用过大
			if (this.free.length > 1000) {
				this.free.length = 1000;
			}
		},

		clear() {
			this.free.length = 0;
		},
	};

	const currentMapTool = ref<"click" | "section" | "row" | "col" | null>(
		null
	);
	const mapTools = ref([
		{ type: "click", icon: "icon-dianji", label: "单选" },
		{ type: "section", icon: "icon-kuangxuan", label: "框选" },
		{ type: "row", icon: "icon-hangxuanze", label: "行选" },
		{ type: "col", icon: "icon-liexuanze", label: "列选" },
		{ type: "clear", icon: "icon-qingchu", label: "清除" },
	]);

	const handleMapToolClick = (tool: { type: string; icon: string }) => {
		if (!mapDrawCanvas.value) return;

		if (tool.type === "clear") {
			// 清除所有标记
			maps.value = [];
			mapIsDrawing.value = false;
			mapSelectionStart.value = null;
			mapSelectionCurrent.value = null;
			currentMapTool.value = null;
			drawMapMarks();
			return;
		}

		// 取消当前绘制
		if (mapIsDrawing.value) {
			mapIsDrawing.value = false;
			mapSelectionStart.value = null;
			mapSelectionCurrent.value = null;
			drawMapMarks();
		}

		currentMapTool.value = tool.type as any;
	};

	// Map模式初始化
	const initMap = () => {
		if (
			!mapContainer.value ||
			!mapLayerCanvas.value ||
			!mapDrawCanvas.value ||
			!props.mapData?.map
		)
			return;

		// 初始化MapWorker数据
		mapWorker.initMapData({
			rowCnt: props.mapData.size.rowCnt,
			colCnt: props.mapData.size.colCnt,
			map: props.mapData.map,
			cellSize: mapCellSize.value,
			mapRatio: props.mapRatio,
		});

		// 动态设置单元格大小和分块大小，根据地图尺寸优化性能
		if (props.mapData.size) {
			const { rowCnt, colCnt } = props.mapData.size;
			const maxSize = Math.max(rowCnt, colCnt);

			// 根据地图大小调整单元格尺寸
			if (maxSize < 100) {
				mapCellSize.value = 15;
				mapChunkSize.value = 50; // 小地图使用小块
			} else if (maxSize < 300) {
				mapCellSize.value = 10;
				mapChunkSize.value = 100; // 中等地图
			} else {
				mapCellSize.value = 8;
				mapChunkSize.value = 200; // 大地图使用大块，减少分块数量

				// 超大地图特殊处理 (例如700x700)
				if (maxSize >= 600) {
					console.log(
						`检测到超大地图: ${rowCnt}x${colCnt}，启用性能优化模式`
					);
					// 更大的分块以减少绘制次数
					mapChunkSize.value = 250;

					// 对于超大地图，减少渲染精度以提高性能
					mapCellSize.value = 6; // 进一步减小单元格尺寸
				}
			}

			// 清除旧缓存
			clearMapLayerCache();
		}

		// 清除tooltip状态
		clearMapTooltip();

		// 清除之前绑定的事件监听器
		if (mapDrawCanvas.value) {
			mapDrawCanvas.value.removeEventListener("click", handleMapClick);
			mapDrawCanvas.value.removeEventListener(
				"mousemove",
				handleMapMouseMove
			);
		}

		document.removeEventListener("mouseup", handleMapMouseUp);

		// 重置状态
		mapScale.value = 1;
		mapIsDrawing.value = false;
		mapSelectionStart.value = null;
		mapSelectionCurrent.value = null;
		mapDragging.value = false;
		mapRenderPending.value = false;

		const containerRect = mapContainer.value.getBoundingClientRect();

		// 获取地图数据尺寸
		const rows = props.mapData.size.rowCnt;
		const cols = props.mapData.size.colCnt;

		// 计算总地图尺寸
		const totalMapWidth = cols * mapCellSize.value * props.mapRatio;
		const totalMapHeight = rows * mapCellSize.value;

		// 设置容器和画布样式，确保它们可见
		if (mapContainer.value) {
			const mapContent = mapContainer.value.querySelector(
				".map-content"
			) as HTMLElement;
			if (mapContent) {
				mapContent.style.width = `${totalMapWidth}px`;
				mapContent.style.height = `${totalMapHeight}px`;
				mapContent.style.position = "absolute";
				mapContent.style.transformOrigin = "0 0";
			}
		}

		// 设置Canvas尺寸
		mapLayerCanvas.value.width = totalMapWidth;
		mapLayerCanvas.value.height = totalMapHeight;
		mapDrawCanvas.value.width = totalMapWidth;
		mapDrawCanvas.value.height = totalMapHeight;

		// 获取并缓存Canvas上下文
		mapLayerContext.value = mapLayerCanvas.value.getContext("2d", {
			willReadFrequently: true,
		});
		mapDrawContext.value = mapDrawCanvas.value.getContext("2d", {
			willReadFrequently: true,
		});

		// 计算初始缩放比例使地图完整显示
		const scaleX = containerRect.width / totalMapWidth;
		const scaleY = containerRect.height / totalMapHeight;
		// 使用Math.min函数确保地图完全显示在容器内，并且无法缩小到更小的比例
		const initialScale = Math.min(scaleX, scaleY);

		// 设置为最小缩放比例
		mapScale.value = initialScale;

		// 记录最小缩放比例，用于限制缩小操作
		mapMinScale.value = initialScale;

		// 更新位置使地图居中
		mapPosition.value = {
			x: (containerRect.width - totalMapWidth * initialScale) / 2,
			y: (containerRect.height - totalMapHeight * initialScale) / 2,
		};

		// 立即应用变换
		if (mapContainer.value) {
			const mapContent = mapContainer.value.querySelector(
				".map-content"
			) as HTMLElement;
			if (mapContent) {
				mapContent.style.transform = `translate(${mapPosition.value.x}px, ${mapPosition.value.y}px) scale(${mapScale.value})`;
			}
		}

		// 清除之前的标记
		if (maps.value.length > 0) {
			mapMarkPool.release([...maps.value]);
			maps.value = [];
		} else {
			maps.value = [];
		}

		// 绘制Map层 - 确保首次绘制
		drawMapLayer();

		// 绘制标记层
		drawMapMarks();

		// 绑定事件
		mapDrawCanvas.value.addEventListener("click", handleMapClick);
		mapDrawCanvas.value.addEventListener("mousemove", handleMapMouseMove);
		document.addEventListener("mouseup", handleMapMouseUp);
	};

	// 获取可视区域的地图范围
	const getVisibleMapRect = () => {
		if (!mapContainer.value || !mapLayerCanvas.value) {
			return { left: 0, top: 0, right: 0, bottom: 0 };
		}

		const containerRect = mapContainer.value.getBoundingClientRect();

		// 计算可视区域对应的地图坐标范围
		const left = Math.max(0, -mapPosition.value.x / mapScale.value);
		const top = Math.max(0, -mapPosition.value.y / mapScale.value);
		const right = Math.min(
			mapLayerCanvas.value.width,
			left + containerRect.width / mapScale.value
		);
		const bottom = Math.min(
			mapLayerCanvas.value.height,
			top + containerRect.height / mapScale.value
		);

		return { left, top, right, bottom };
	};

	const drawMapLayer = () => {
		if (!mapLayerCanvas.value || !props.mapData) return;

		// 优先使用缓存的上下文
		const ctx =
			mapLayerContext.value ||
			mapLayerCanvas.value.getContext("2d", { willReadFrequently: true });
		if (!ctx) return;

		// 获取地图数据和尺寸
		const map = props.mapData.map;
		const rows = props.mapData.size.rowCnt;
		const cols = props.mapData.size.colCnt;

		// 计算地图总尺寸
		const totalMapWidth = cols * mapCellSize.value * props.mapRatio;
		const totalMapHeight = rows * mapCellSize.value;

		// 清除Canvas
		ctx.clearRect(0, 0, totalMapWidth, totalMapHeight);

		// 保存绘图上下文状态
		ctx.save();

		// 检查缩放比例是否变化，如果变化则清除缓存
		if (lastMapRenderScale.value !== mapScale.value) {
			mapLayerCache.value.clear();
			lastMapRenderScale.value = mapScale.value;
		}

		// 根据缩放级别选择渲染策略
		const scaleFactor = mapScale.value;

		// 获取可视区域
		const visibleRect = getVisibleMapRect();

		if (scaleFactor < 0.5) {
			// 低缩放级别: 绘制降采样版本
			drawLowResolutionMap(ctx, map, rows, cols, visibleRect);
		} else if (scaleFactor >= 0.5) {
			// 高缩放级别: 使用分块渲染
			drawHighResolutionMap(ctx, map, rows, cols, visibleRect);
		}

		// 绘制所有单元格的背景网格
		const showInactiveGrid = false; // 设置为false避免全局绘制网格线
		if (showInactiveGrid) {
			drawGridLines(ctx, rows, cols, totalMapWidth, totalMapHeight);
		}

		// 恢复上下文状态
		ctx.restore();
	};

	// 绘制低分辨率地图（降采样）
	const drawLowResolutionMap = (
		ctx: CanvasRenderingContext2D,
		map: number[][],
		rows: number,
		cols: number,
		visibleRect: {
			left: number;
			top: number;
			right: number;
			bottom: number;
		}
	) => {
		// 降采样因子，缩放越小，采样因子越大
		const downscaleFactor = mapScale.value < 0.2 ? 8 : 4;

		// 计算可视区域内的行列范围
		const startRow = Math.max(
			0,
			Math.floor(visibleRect.top / mapCellSize.value)
		);
		const endRow = Math.min(
			rows,
			Math.ceil(visibleRect.bottom / mapCellSize.value)
		);
		const startCol = Math.max(
			0,
			Math.floor(visibleRect.left / (mapCellSize.value * props.mapRatio))
		);
		const endCol = Math.min(
			cols,
			Math.ceil(visibleRect.right / (mapCellSize.value * props.mapRatio))
		);

		// 缓存键
		const cacheKey = `low_${downscaleFactor}_${startRow}_${startCol}_${endRow}_${endCol}`;

		// 检查缓存
		if (mapLayerCache.value.has(cacheKey)) {
			const cachedData = mapLayerCache.value.get(cacheKey);
			if (cachedData) {
				ctx.putImageData(cachedData, 0, 0);
				return;
			}
		}

		// 设置填充样式
		ctx.fillStyle = "rgba(0, 255, 0, 0.4)";
		ctx.beginPath();

		// 对齐到降采样网格
		const alignedStartRow =
			Math.floor(startRow / downscaleFactor) * downscaleFactor;
		const alignedStartCol =
			Math.floor(startCol / downscaleFactor) * downscaleFactor;

		// 遍历可视范围内的降采样块
		for (let row = alignedStartRow; row < endRow; row += downscaleFactor) {
			for (
				let col = alignedStartCol;
				col < endCol;
				col += downscaleFactor
			) {
				// 检查这个区域是否有活跃单元格
				let hasActiveCell = false;
				const checkEndRow = Math.min(row + downscaleFactor, rows);
				const checkEndCol = Math.min(col + downscaleFactor, cols);

				// 优化: 一旦找到一个活跃单元格就停止查找
				outerLoop: for (let r = row; r < checkEndRow; r++) {
					for (let c = col; c < checkEndCol; c++) {
						if (map[r] && map[r][c] === 1) {
							hasActiveCell = true;
							break outerLoop;
						}
					}
				}

				if (hasActiveCell) {
					const x = col * mapCellSize.value * props.mapRatio;
					const y = row * mapCellSize.value;
					const width =
						Math.min(downscaleFactor, cols - col) *
						mapCellSize.value *
						props.mapRatio;
					const height =
						Math.min(downscaleFactor, rows - row) *
						mapCellSize.value;

					ctx.rect(x, y, width, height);
				}
			}
		}

		ctx.fill();

		// 边缘描边 - 增强边框可见性
		ctx.strokeStyle = "rgba(0, 0, 0, 0.7)";
		ctx.lineWidth = mapScale.value < 0.3 ? 0.7 : 0.9;
		ctx.stroke();

		// 缓存渲染结果
		try {
			const imageData = ctx.getImageData(
				0,
				0,
				mapLayerCanvas.value!.width,
				mapLayerCanvas.value!.height
			);
			mapLayerCache.value.set(cacheKey, imageData);
		} catch (e) {
			console.error("缓存地图渲染失败:", e);
		}
	};

	// 绘制高分辨率地图（分块渲染）
	const drawHighResolutionMap = (
		ctx: CanvasRenderingContext2D,
		map: number[][],
		rows: number,
		cols: number,
		visibleRect: {
			left: number;
			top: number;
			right: number;
			bottom: number;
		}
	) => {
		// 计算可视区域内的行列范围
		const startRow = Math.max(
			0,
			Math.floor(visibleRect.top / mapCellSize.value)
		);
		const endRow = Math.min(
			rows,
			Math.ceil(visibleRect.bottom / mapCellSize.value)
		);
		const startCol = Math.max(
			0,
			Math.floor(visibleRect.left / (mapCellSize.value * props.mapRatio))
		);
		const endCol = Math.min(
			cols,
			Math.ceil(visibleRect.right / (mapCellSize.value * props.mapRatio))
		);

		// 计算分块的数量
		const chunkSize = mapChunkSize.value;
		const rowChunks = Math.ceil((endRow - startRow) / chunkSize);
		const colChunks = Math.ceil((endCol - startCol) / chunkSize);

		// 批量绘制活跃单元格，按块处理
		for (let chunkRowIdx = 0; chunkRowIdx < rowChunks; chunkRowIdx++) {
			for (let chunkColIdx = 0; chunkColIdx < colChunks; chunkColIdx++) {
				const chunkStartRow = startRow + chunkRowIdx * chunkSize;
				const chunkStartCol = startCol + chunkColIdx * chunkSize;
				const chunkEndRow = Math.min(chunkStartRow + chunkSize, endRow);
				const chunkEndCol = Math.min(chunkStartCol + chunkSize, endCol);

				// 缓存键
				const cacheKey = `high_${chunkStartRow}_${chunkStartCol}_${chunkEndRow}_${chunkEndCol}`;

				// 检查缓存
				if (mapLayerCache.value.has(cacheKey)) {
					const cachedData = mapLayerCache.value.get(cacheKey);
					if (cachedData) {
						// 计算块在画布上的位置
						const chunkX =
							chunkStartCol * mapCellSize.value * props.mapRatio;
						const chunkY = chunkStartRow * mapCellSize.value;
						ctx.putImageData(cachedData, chunkX, chunkY);
						continue;
					}
				}

				// 计算块的尺寸
				const chunkWidth =
					(chunkEndCol - chunkStartCol) *
					mapCellSize.value *
					props.mapRatio;
				const chunkHeight =
					(chunkEndRow - chunkStartRow) * mapCellSize.value;

				// 创建离屏canvas来渲染这个块
				const chunkCanvas = document.createElement("canvas");
				chunkCanvas.width = chunkWidth;
				chunkCanvas.height = chunkHeight;
				const chunkCtx = chunkCanvas.getContext("2d", {
					willReadFrequently: true,
				});

				if (!chunkCtx) continue;

				chunkCtx.fillStyle = "rgba(0, 255, 0, 0.4)";

				// 根据缩放级别设置边框样式
				if (mapScale.value >= 2) {
					// 高缩放级别时使用黑色边框 - 更清晰的分隔
					chunkCtx.strokeStyle = "rgba(0, 0, 0, 0.8)";
					chunkCtx.lineWidth = 1.0;
				} else if (mapScale.value >= 1) {
					// 中等缩放级别
					chunkCtx.strokeStyle = "rgba(0, 0, 0, 0.7)";
					chunkCtx.lineWidth = 0.8;
				} else {
					// 低缩放级别
					chunkCtx.strokeStyle = "rgba(0, 0, 0, 0.6)";
					chunkCtx.lineWidth = 0.5;
				}

				// 使用路径批量填充
				let hasActiveCells = false;

				// 第一遍：收集需要绘制的矩形
				const rectangles: {
					x: number;
					y: number;
					width: number;
					height: number;
				}[] = [];

				// 尝试合并连续的单元格为更大的矩形
				for (let row = chunkStartRow; row < chunkEndRow; row++) {
					let startCol = -1;
					let currCol = chunkStartCol;

					while (currCol < chunkEndCol) {
						// 跳过无效单元格
						while (
							currCol < chunkEndCol &&
							(!map[row] || map[row][currCol] !== 1)
						) {
							currCol++;
						}

						if (currCol >= chunkEndCol) break;

						// 找到有效起始点
						startCol = currCol;

						// 继续查找连续有效单元格
						while (
							currCol < chunkEndCol &&
							map[row] &&
							map[row][currCol] === 1
						) {
							currCol++;
						}

						// 找到了连续区域，添加矩形
						if (startCol !== -1) {
							const rectX =
								(startCol - chunkStartCol) *
								mapCellSize.value *
								props.mapRatio;
							const rectY =
								(row - chunkStartRow) * mapCellSize.value;
							const rectWidth =
								(currCol - startCol) *
								mapCellSize.value *
								props.mapRatio;
							const rectHeight = mapCellSize.value;

							rectangles.push({
								x: rectX,
								y: rectY,
								width: rectWidth,
								height: rectHeight,
							});
							hasActiveCells = true;
						}
					}
				}

				// 绘制收集到的矩形
				if (hasActiveCells) {
					// 批量填充
					chunkCtx.beginPath();
					for (const rect of rectangles) {
						chunkCtx.rect(rect.x, rect.y, rect.width, rect.height);
					}
					chunkCtx.fill();

					// 高缩放级别时，为每个单元格单独描边以增强可视性
					if (mapScale.value >= 1.0) {
						// 单独绘制每个单元格的边框，而非合并区域
						chunkCtx.beginPath();
						for (
							let row = chunkStartRow;
							row < chunkEndRow;
							row++
						) {
							for (
								let col = chunkStartCol;
								col < chunkEndCol;
								col++
							) {
								if (map[row] && map[row][col] === 1) {
									const x =
										(col - chunkStartCol) *
										mapCellSize.value *
										props.mapRatio;
									const y =
										(row - chunkStartRow) *
										mapCellSize.value;
									const width =
										mapCellSize.value * props.mapRatio;
									const height = mapCellSize.value;

									chunkCtx.strokeRect(x, y, width, height);
								}
							}
						}
					} else {
						// 低缩放级别时，只绘制矩形块的边框
						chunkCtx.beginPath();
						for (const rect of rectangles) {
							chunkCtx.rect(
								rect.x,
								rect.y,
								rect.width,
								rect.height
							);
						}
						chunkCtx.stroke();
					}

					// 获取块的图像数据并缓存
					const chunkImageData = chunkCtx.getImageData(
						0,
						0,
						chunkWidth,
						chunkHeight
					);
					mapLayerCache.value.set(cacheKey, chunkImageData);

					// 将块绘制到主画布
					const chunkX =
						chunkStartCol * mapCellSize.value * props.mapRatio;
					const chunkY = chunkStartRow * mapCellSize.value;
					ctx.putImageData(chunkImageData, chunkX, chunkY);
				}
			}
		}
	};

	// 绘制网格线
	const drawGridLines = (
		ctx: CanvasRenderingContext2D,
		rows: number,
		cols: number,
		totalMapWidth: number,
		totalMapHeight: number
	) => {
		// 获取可视区域
		const visibleRect = getVisibleMapRect();

		// 计算可视网格线范围
		const startRow = Math.max(
			0,
			Math.floor(visibleRect.top / mapCellSize.value)
		);
		const endRow = Math.min(
			rows,
			Math.ceil(visibleRect.bottom / mapCellSize.value) + 1
		);
		const startCol = Math.max(
			0,
			Math.floor(visibleRect.left / (mapCellSize.value * props.mapRatio))
		);
		const endCol = Math.min(
			cols,
			Math.ceil(
				visibleRect.right / (mapCellSize.value * props.mapRatio)
			) + 1
		);

		// 根据缩放级别调整网格线样式
		if (mapScale.value >= 1.5) {
			// 高缩放级别 - 更清晰的网格线
			ctx.strokeStyle = "rgba(0, 0, 0, 0.3)";
			ctx.lineWidth = 0.5;
		} else if (mapScale.value >= 0.8) {
			// 中等缩放级别
			ctx.strokeStyle = "rgba(100, 100, 100, 0.25)";
			ctx.lineWidth = 0.5;
		} else {
			// 低缩放级别 - 淡化的网格线
			ctx.strokeStyle = "rgba(200, 200, 200, 0.2)";
			ctx.lineWidth = 0.5;
		}

		// 绘制水平线
		ctx.beginPath();
		for (let row = startRow; row <= endRow; row++) {
			const y = row * mapCellSize.value;
			ctx.moveTo(visibleRect.left, y);
			ctx.lineTo(visibleRect.right, y);
		}

		// 绘制垂直线
		for (let col = startCol; col <= endCol; col++) {
			const x = col * mapCellSize.value * props.mapRatio;
			ctx.moveTo(x, visibleRect.top);
			ctx.lineTo(x, visibleRect.bottom);
		}

		ctx.stroke();
	};

	// 清除Map层缓存
	const clearMapLayerCache = () => {
		mapLayerCache.value.clear();
	};

	// 绘制标记层
	const drawMapMarks = () => {
		if (!mapDrawCanvas.value || !props.mapData?.map) return;

		// 使用缓存的上下文
		const ctx =
			mapDrawContext.value ||
			mapDrawCanvas.value.getContext("2d", { willReadFrequently: true });
		if (!ctx) return;

		// 获取地图数据尺寸
		const rows = props.mapData.size.rowCnt;
		const cols = props.mapData.size.colCnt;

		// 计算总地图尺寸
		const totalMapWidth = cols * mapCellSize.value * props.mapRatio;
		const totalMapHeight = rows * mapCellSize.value;

		// 确保画布尺寸与地图层画布一致
		if (
			mapDrawCanvas.value.width !== totalMapWidth ||
			mapDrawCanvas.value.height !== totalMapHeight
		) {
			mapDrawCanvas.value.width = totalMapWidth;
			mapDrawCanvas.value.height = totalMapHeight;
		}

		// 清除画布
		ctx.clearRect(0, 0, totalMapWidth, totalMapHeight);

		// 保存当前状态
		ctx.save();

		// 使用Worker计算和绘制单元格填充
		if (maps.value.length > 0) {
			// 先绘制填充部分
			for (let row = 0; row < rows; row++) {
				let markInRow = maps.value.filter((mark) => mark.row === row);

				if (markInRow.length === 0) continue;

				// 按列排序
				markInRow.sort((a, b) => a.col - b.col);

				// 合并连续的标记
				let currentMark: {
					row: number;
					col: number;
					id: string;
					fillColor?: string;
					strokeColor?: string;
				} | null = null;
				let startCol = -1;
				let endCol = -1;

				for (let i = 0; i <= markInRow.length; i++) {
					const mark = i < markInRow.length ? markInRow[i] : null;

					// 如果是新的一组或者最后一个标记
					if (
						currentMark === null ||
						mark === null ||
						mark.col !== endCol + 1 ||
						mark.fillColor !== currentMark.fillColor ||
						mark.strokeColor !== currentMark.strokeColor
					) {
						// 绘制之前收集的一组连续标记
						if (currentMark !== null && endCol >= startCol) {
							// 计算连续标记的位置和大小
							const x =
								startCol * mapCellSize.value * props.mapRatio;
							const y = row * mapCellSize.value;
							const width =
								(endCol - startCol + 1) *
								mapCellSize.value *
								props.mapRatio;
							const height = mapCellSize.value;

							ctx.fillStyle =
								currentMark.fillColor || "rgba(255, 0, 0, 0.3)";
							ctx.beginPath();
							ctx.rect(x, y, width, height);
							ctx.fill();
						}

						// 开始新的一组
						if (mark !== null) {
							currentMark = mark;
							startCol = mark.col;
							endCol = mark.col;
						} else {
							currentMark = null;
						}
					} else {
						// 继续当前一组
						endCol = mark.col;
					}
				}
			}

			// 使用Worker计算并绘制边界
			mapWorker.findBlockBorders(maps.value, ({ blocks, colors }) => {
				// 如果没有找到边界或者组件已经卸载，直接返回
				if (!blocks || !colors || !mapDrawCanvas.value) return;

				try {
					// 绘制每个区块的边界
					blocks.forEach((blockEdges, index) => {
						if (!blockEdges || blockEdges.length === 0) return;

						const color = colors[index] || "red";

						// 设置边框样式
						ctx.strokeStyle = color;
						ctx.lineWidth = 1;
						ctx.beginPath();

						// 绘制边界线
						blockEdges.forEach((edge) => {
							if (!edge) return;

							const { x, y, side } = edge;
							const width = mapCellSize.value * props.mapRatio;
							const height = mapCellSize.value;

							switch (side) {
								case "top":
									ctx.moveTo(x, y);
									ctx.lineTo(x + width, y);
									break;
								case "bottom":
									ctx.moveTo(x, y);
									ctx.lineTo(x + width, y);
									break;
								case "left":
									ctx.moveTo(x, y);
									ctx.lineTo(x, y + height);
									break;
								case "right":
									ctx.moveTo(x, y);
									ctx.lineTo(x, y + height);
									break;
							}
						});

						ctx.stroke();
					});
				} catch (e) {
					console.error("绘制边界错误:", e);
				}
			});
		}

		// 绘制当前选区预览
		if (
			mapIsDrawing.value &&
			mapSelectionStart.value &&
			mapSelectionCurrent.value &&
			currentMapTool.value === "section"
		) {
			const startRow = Math.min(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const startCol = Math.min(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);
			const endRow = Math.max(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const endCol = Math.max(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);

			// 计算选区位置和大小
			const x = startCol * mapCellSize.value * props.mapRatio;
			const y = startRow * mapCellSize.value;
			const width =
				(endCol - startCol + 1) * mapCellSize.value * props.mapRatio;
			const height = (endRow - startRow + 1) * mapCellSize.value;

			// 先只绘制填充
			ctx.fillStyle = drawConfig.fill || "rgba(255, 0, 0, 0.3)";
			ctx.beginPath();
			ctx.rect(x, y, width, height);
			ctx.fill();

			// 单独绘制边框
			ctx.strokeStyle = drawConfig.stroke || "red";
			ctx.lineWidth = 1.5; // 稍微粗一点的边框以示区分
			ctx.strokeRect(x, y, width, height);
		}

		// 恢复状态
		ctx.restore();
	};

	// 处理Map点击
	const handleMapClick = (e: MouseEvent) => {
		if (!mapDrawCanvas.value || !props.mapData?.map) return;

		// 先阻止默认行为
		e.preventDefault();

		// 获取canvas的边界框位置
		const rect = mapDrawCanvas.value.getBoundingClientRect();

		// 计算鼠标在canvas上的相对位置
		const mouseX = e.clientX - rect.left;
		const mouseY = e.clientY - rect.top;

		// 获取相对坐标
		const contentX = mouseX / mapScale.value;
		const contentY = mouseY / mapScale.value;

		// 映射到地图单元格坐标
		const col = Math.floor(contentX / (mapCellSize.value * props.mapRatio));
		const row = Math.floor(contentY / mapCellSize.value);

		// 检查单元格是否在有效范围内
		if (
			row < 0 ||
			row >= props.mapData.map.length ||
			col < 0 ||
			col >= (props.mapData.map[row]?.length || 0)
		) {
			return;
		}

		// 检查单元格是否有效
		if (props.mapData.map[row][col] !== 1) {
			return;
		}

		// 处理工具动作
		switch (currentMapTool.value) {
			case "click":
				handleMapClickMode(row, col);
				break;
			case "section":
				handleMapSectionMode(row, col);
				break;
			case "row":
				handleMapRowMode(row);
				break;
			case "col":
				handleMapColMode(col);
				break;
			default:
		}
	};

	// 点击模式处理
	const handleMapClickMode = (row: number, col: number) => {
		// 检查单元格是否有效
		if (
			!props.mapData ||
			!props.mapData.map[row] ||
			props.mapData.map[row][col] !== 1
		) {
			return;
		}

		const existingMarkIndex = maps.value.findIndex(
			(mark) => mark.row === row && mark.col === col
		);

		if (existingMarkIndex !== -1) {
			// 如果已标记，则取消标记
			const removedMark = maps.value.splice(existingMarkIndex, 1)[0];
			// 将移除的标记放回对象池
			mapMarkPool.release([removedMark]);
		} else {
			// 否则添加标记，使用当前的绘制颜色
			const newMark = mapMarkPool.get(row, col);
			newMark.fillColor =
				typeof drawConfig.fill === "string"
					? drawConfig.fill
					: "rgba(255, 0, 0, 0.3)";
			newMark.strokeColor =
				typeof drawConfig.stroke === "string"
					? drawConfig.stroke
					: "red";
			maps.value.push(newMark);
		}

		// 重置点选状态并重绘
		currentMapTool.value = null;
		drawMapMarks();
	};

	// 框选模式处理
	const handleMapSectionMode = (row: number, col: number) => {
		// 检查单元格是否有效
		if (
			!props.mapData ||
			!props.mapData.map[row] ||
			props.mapData.map[row][col] !== 1
		) {
			return;
		}

		if (!mapIsDrawing.value) {
			// 开始框选
			mapSelectionStart.value = { row, col };
			mapSelectionCurrent.value = { row, col };
			mapIsDrawing.value = true;

			// 立即绘制初始框选预览
			drawMapMarks();
		} else {
			// 完成框选
			if (!mapSelectionStart.value) return;

			// 确保更新当前选择位置
			mapSelectionCurrent.value = { row, col };

			const startRow = Math.min(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const startCol = Math.min(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);
			const endRow = Math.max(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const endCol = Math.max(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);

			// 准备颜色属性
			const fillColor =
				typeof drawConfig.fill === "string"
					? drawConfig.fill
					: "rgba(255, 0, 0, 0.3)";
			const strokeColor =
				typeof drawConfig.stroke === "string"
					? drawConfig.stroke
					: "red";

			// 使用Worker计算有效单元格
			mapWorker.calculateSelectedArea(
				startRow,
				startCol,
				endRow,
				endCol,
				(validCells) => {
					try {
						// 确保有效单元格存在
						if (!validCells || !Array.isArray(validCells)) {
							console.warn("无法获取有效单元格");
							return;
						}

						// 创建新标记
						const newMarks = validCells.map((cell) => {
							const newMark = mapMarkPool.get(cell.row, cell.col);
							newMark.fillColor = fillColor;
							newMark.strokeColor = strokeColor;
							return newMark;
						});

						// 批量添加到maps数组
						if (newMarks.length > 0) {
							maps.value.push(...newMarks);
						}

						// 重置框选状态
						mapIsDrawing.value = false;
						mapSelectionStart.value = null;
						mapSelectionCurrent.value = null;
						currentMapTool.value = null;

						// 重绘标记
						drawMapMarks();
					} catch (e) {
						console.error("处理选择区域错误:", e);
						// 重置框选状态，确保不会卡住
						mapIsDrawing.value = false;
						mapSelectionStart.value = null;
						mapSelectionCurrent.value = null;
					}
				}
			);
		}
	};

	// 行选择模式处理
	const handleMapRowMode = (row: number) => {
		if (
			!props.mapData ||
			!props.mapData.map ||
			row >= props.mapData.map.length
		)
			return;

		// 检查行是否已全部选中
		const rowData = props.mapData.map[row];
		if (!rowData) return;

		const validCells = rowData.filter((cell) => cell === 1).length;
		const selectedCount = maps.value.filter(
			(mark) => mark.row === row
		).length;

		// 准备颜色属性
		const fillColor =
			typeof drawConfig.fill === "string"
				? drawConfig.fill
				: "rgba(255, 0, 0, 0.3)";
		const strokeColor =
			typeof drawConfig.stroke === "string" ? drawConfig.stroke : "red";

		if (selectedCount === validCells) {
			// 如果全部选中，则取消选择
			const removedMarks = maps.value.filter((mark) => mark.row === row);
			maps.value = maps.value.filter((mark) => mark.row !== row);

			// 回收对象到对象池
			if (removedMarks.length > 0) {
				mapMarkPool.release(removedMarks);
			}
		} else {
			// 创建新标记的批量数组
			const newMarks: {
				row: number;
				col: number;
				id: string;
				fillColor?: string;
				strokeColor?: string;
			}[] = [];

			// 选择整行
			for (let col = 0; col < rowData.length; col++) {
				if (rowData[col] === 1) {
					const existingMarkIndex = maps.value.findIndex(
						(mark) => mark.row === row && mark.col === col
					);
					if (existingMarkIndex === -1) {
						const newMark = mapMarkPool.get(row, col);
						newMark.fillColor = fillColor;
						newMark.strokeColor = strokeColor;
						newMarks.push(newMark);
					}
				}
			}

			// 批量添加
			if (newMarks.length > 0) {
				maps.value.push(...newMarks);
			}
		}

		// 完成后重置工具
		currentMapTool.value = null;
		drawMapMarks();
	};

	// 列选择模式处理
	const handleMapColMode = (col: number) => {
		if (!props.mapData?.map) return;

		// 检查列是否已全部选中
		const validCells = props.mapData.map.filter(
			(row, rowIndex) => row && row[col] === 1
		).length;
		const selectedCount = maps.value.filter(
			(mark) => mark.col === col
		).length;

		// 准备颜色属性
		const fillColor =
			typeof drawConfig.fill === "string"
				? drawConfig.fill
				: "rgba(255, 0, 0, 0.3)";
		const strokeColor =
			typeof drawConfig.stroke === "string" ? drawConfig.stroke : "red";

		if (selectedCount === validCells) {
			// 如果全部选中，则取消选择
			const removedMarks = maps.value.filter((mark) => mark.col === col);
			maps.value = maps.value.filter((mark) => mark.col !== col);

			// 回收对象到对象池
			if (removedMarks.length > 0) {
				mapMarkPool.release(removedMarks);
			}
		} else {
			// 创建新标记的批量数组
			const newMarks: {
				row: number;
				col: number;
				id: string;
				fillColor?: string;
				strokeColor?: string;
			}[] = [];

			// 选择整列
			for (let row = 0; row < props.mapData.map.length; row++) {
				if (
					props.mapData.map[row] &&
					props.mapData.map[row][col] === 1
				) {
					const existingMarkIndex = maps.value.findIndex(
						(mark) => mark.row === row && mark.col === col
					);
					if (existingMarkIndex === -1) {
						const newMark = mapMarkPool.get(row, col);
						newMark.fillColor = fillColor;
						newMark.strokeColor = strokeColor;
						newMarks.push(newMark);
					}
				}
			}

			// 批量添加
			if (newMarks.length > 0) {
				maps.value.push(...newMarks);
			}
		}

		// 完成后重置工具
		currentMapTool.value = null;
		drawMapMarks();
	};

	// 优化视口内单元格计算，减少主线程负担
	const optimizeMapRendering = throttle(
		() => {
			if (!mapContainer.value || screenMode.value !== "video-off") return;

			// 计算当前视口信息
			const viewportInfo = {
				left: -mapPosition.value.x / mapScale.value,
				top: -mapPosition.value.y / mapScale.value,
				width: mapContainer.value.clientWidth / mapScale.value,
				height: mapContainer.value.clientHeight / mapScale.value,
				scale: mapScale.value,
			};

			// 使用Worker计算视口内可见的单元格
			mapWorker.calculateVisibleCells(viewportInfo, (visibleCells) => {
				try {
					// 确保可见单元格存在
					if (!visibleCells || !Array.isArray(visibleCells)) {
						console.warn("无法获取可见单元格");
						return;
					}

					// 仅在需要大量渲染时使用视口优化
					if (maps.value.length > 500) {
						mapRenderPending.value = true;
						// 优化性能 - 在数据量非常大时可以添加具体的可见区域渲染逻辑
					}
				} catch (e) {
					console.error("处理可见单元格错误:", e);
				}
			});
		},
		200,
		{ leading: true, trailing: true }
	);

	// Map 模式 滚轮缩放
	const handleMapWheel = throttle(
		(e: WheelEvent) => {
			e.preventDefault();

			// 计算缩放增量和新的缩放比例
			const delta = e.deltaY > 0 ? -0.1 : 0.1;

			// 使用mapMinScale作为最小缩放限制，不允许缩小到比初始缩放还小
			const newScale = Math.min(
				Math.max(mapScale.value + delta, mapMinScale.value),
				5
			);

			// 只有当缩放比例变化时才处理
			if (newScale !== mapScale.value) {
				// 获取地图容器的边界框
				const rect = mapContainer.value?.getBoundingClientRect();
				if (!rect) return;

				// 处理缩小到最小比例时的限制
				if (delta < 0 && mapScale.value <= mapMinScale.value) {
					// 已经是最小缩放比例，不允许继续缩小
					return;
				}

				// 计算鼠标相对于容器的位置
				const mouseX = e.clientX - rect.left;
				const mouseY = e.clientY - rect.top;

				// 计算鼠标相对于当前变换后地图内容的位置
				const contentX =
					(mouseX - mapPosition.value.x) / mapScale.value;
				const contentY =
					(mouseY - mapPosition.value.y) / mapScale.value;

				// 缩放变化较大时清除缓存
				if (Math.abs(newScale - mapScale.value) > 0.3) {
					clearMapLayerCache();
				}

				// 更新缩放值
				mapScale.value = newScale;

				// 调整位置，使鼠标指针下的内容保持不变
				// 新位置 = 鼠标位置 - (鼠标相对于内容的位置 * 新缩放比例)
				mapPosition.value = {
					x: mouseX - contentX * newScale,
					y: mouseY - contentY * newScale,
				};

				// 直接应用CSS变换，避免重绘
				if (mapContainer.value) {
					const mapContent = mapContainer.value.querySelector(
						".map-content"
					) as HTMLElement;
					if (mapContent) {
						mapContent.style.transform = `translate(${mapPosition.value.x}px, ${mapPosition.value.y}px) scale(${mapScale.value})`;
						// 确保变换原点在左上角
						mapContent.style.transformOrigin = "0 0";
					}
				}

				// 延迟更新绘制层，避免频繁重绘
				if (mapScale.value < 0.5) {
					// 在低缩放级别立即重绘，因为降采样绘制速度较快
					drawMapLayer();
					drawMapMarks();
				} else {
					// 高缩放级别使用requestAnimationFrame延迟重绘，避免频繁重绘导致卡顿
					requestAnimationFrame(() => {
						if (mapLayerCanvas.value) {
							drawMapLayer();
							drawMapMarks();
						}
					});
				}

				// 调用视口优化
				optimizeMapRendering();
			}
		},
		16,
		{ leading: true, trailing: true }
	);

	// Map 模式 拖动相关
	const handleMapDragStart = (e: MouseEvent) => {
		// 右键点击处理
		if (e.button === 2) {
			mapIsDrawing.value = false;
			mapSelectionStart.value = null;
			mapSelectionCurrent.value = null;
			currentMapTool.value = null;
			drawMapMarks();
			return;
		}

		// 如果当前有选择工具，则不启动拖动
		if (currentMapTool.value) return;

		// 启动拖动
		mapDragging.value = true;
		mapDragStartPosition.value = {
			x: e.clientX,
			y: e.clientY,
		};
		mapDragLastPosition.value = { ...mapPosition.value };

		// 确保事件监听器只添加一次
		document.removeEventListener("mousemove", handleMapMouseMove);
		document.removeEventListener("mouseup", handleMapMouseUp);

		document.addEventListener("mousemove", handleMapMouseMove);
		document.addEventListener("mouseup", handleMapMouseUp, { once: true });
	};

	// 处理Map鼠标移动
	const handleMapMouseMove = throttle(
		(e: MouseEvent) => {
			if (!mapDrawCanvas.value || !props.mapData?.map) return;

			// 处理拖动
			if (mapDragging.value) {
				const deltaX = e.clientX - mapDragStartPosition.value.x;
				const deltaY = e.clientY - mapDragStartPosition.value.y;

				mapPosition.value = {
					x: mapDragLastPosition.value.x + deltaX,
					y: mapDragLastPosition.value.y + deltaY,
				};

				// 直接应用CSS变换，避免重绘
				if (mapContainer.value) {
					const mapContent = mapContainer.value.querySelector(
						".map-content"
					) as HTMLElement;
					if (mapContent) {
						mapContent.style.transform = `translate(${mapPosition.value.x}px, ${mapPosition.value.y}px) scale(${mapScale.value})`;
					}
				}

				// 清除tooltip
				clearMapTooltip();

				// 使用节流控制重绘频率
				// 判断拖动距离，如果拖动超过一定阈值才触发视口优化和重绘
				const dragDistance = Math.sqrt(
					deltaX * deltaX + deltaY * deltaY
				);
				if (dragDistance > 20) {
					// 如果拖动距离较大，重置拖动起始位置以便计算下一次拖动距离
					mapDragStartPosition.value = {
						x: e.clientX,
						y: e.clientY,
					};
					mapDragLastPosition.value = { ...mapPosition.value };

					// 仅在缩放级别较低时实时更新地图渲染
					if (mapScale.value < 0.5) {
						requestAnimationFrame(() => {
							// 只渲染地图层，不更新标记层以提高性能
							drawMapLayer();
						});
					}
				}

				return;
			}

			// 获取canvas边界框位置
			const rect = mapDrawCanvas.value.getBoundingClientRect();

			// 计算鼠标在canvas上的相对位置
			const mouseX = e.clientX - rect.left;
			const mouseY = e.clientY - rect.top;

			// 精确计算内容坐标
			const contentX = mouseX / mapScale.value;
			const contentY = mouseY / mapScale.value;

			// 精确计算单元格坐标（使用Math.floor确保正确舍入）
			const col = Math.floor(
				contentX / (mapCellSize.value * props.mapRatio)
			);
			const row = Math.floor(contentY / mapCellSize.value);

			// 处理框选预览
			if (
				mapIsDrawing.value &&
				mapSelectionStart.value &&
				currentMapTool.value === "section"
			) {
				clearMapTooltip();

				// 更新当前选择位置
				mapSelectionCurrent.value = { row, col };

				// 确保单元格在有效范围内
				if (
					row >= 0 &&
					row < props.mapData.map.length &&
					col >= 0 &&
					col < (props.mapData.map[row]?.length || 0) &&
					props.mapData.map[row][col] === 1
				) {
					// 实时更新绘制层以显示预览
					drawMapMarks();
				}

				return;
			}

			// 处理tooltip显示
			if (
				!mapHoverPosition.value ||
				mapHoverPosition.value.row !== row ||
				mapHoverPosition.value.col !== col
			) {
				// 清除旧的定时器
				if (mapHoverTimer.value !== null) {
					window.clearTimeout(mapHoverTimer.value);
					mapHoverTimer.value = null;
				}

				// 更新hover位置
				mapHoverPosition.value = { row, col };

				// 验证单元格是否在有效范围内且值为1（只有有效单元格才显示tooltip）
				if (
					row >= 0 &&
					row < props.mapData.map.length &&
					col >= 0 &&
					col < (props.mapData.map[row]?.length || 0) &&
					props.mapData.map[row][col] === 1
				) {
					// 设置300ms后显示tooltip
					mapHoverTimer.value = window.setTimeout(() => {
						showMapTooltip.value = true;
						// 正确显示行列信息
						mapTooltipText.value = `行: ${row}, 列: ${col}`;

						// 使用鼠标位置直接定位tooltip，添加少量偏移使其不遮挡鼠标
						mapTooltipStyle.value = {
							left: `${e.clientX - 40}px`,
							top: `${e.clientY - 110}px`, // 在鼠标上方显示
						};
					}, 300);
				} else {
					clearMapTooltip();
				}
			} else if (showMapTooltip.value) {
				// 如果tooltip已显示且仍在同一单元格，只更新位置
				if (props.mapData.map[row]?.[col] === 1) {
					// 使用鼠标位置直接更新tooltip
					mapTooltipStyle.value = {
						left: `${e.clientX - 40}px`,
						top: `${e.clientY - 110}px`, // 在鼠标上方显示
					};
				} else {
					clearMapTooltip();
				}
			}
		},
		16,
		{ leading: true, trailing: true }
	);

	// 处理Map鼠标抬起
	const handleMapMouseUp = (e: MouseEvent) => {
		if (mapDragging.value) {
			mapDragging.value = false;

			// 拖动结束后重新绘制地图和标记
			if (mapLayerCanvas.value && mapDrawCanvas.value) {
				// 使用requestAnimationFrame避免阻塞UI
				requestAnimationFrame(() => {
					// 确保组件仍然存在
					if (mapLayerCanvas.value && mapDrawCanvas.value) {
						drawMapLayer();
						drawMapMarks();
					}
				});
			}
		}
	};

	// 清除Map tooltip
	const clearMapTooltip = () => {
		if (mapHoverTimer.value !== null) {
			window.clearTimeout(mapHoverTimer.value);
			mapHoverTimer.value = null;
		}
		showMapTooltip.value = false;
		mapHoverPosition.value = null;
		mapTooltipText.value = ""; // 清空文本内容
		// 重置样式对象引用
		mapTooltipStyle.value = {
			left: "0px",
			top: "0px",
		};
	};

	// 视图变化时触发可视标记加载
	const handleViewportChange = debounce(() => {
		if (!mapPosition.value || !mapContainer.value) return;

		// 检查是否需要加载更多标记
		loadVisibleMapMarks();
	}, 200);

	// 监听缩放和平移事件
	watch(() => mapPosition.value, handleViewportChange, { deep: true });
	watch(() => mapScale.value, handleViewportChange);
	watch(() => mapContainer.value?.offsetWidth, handleViewportChange);

	// 加载可视区域内的标记
	const loadVisibleMapMarks = async () => {
		// 检查是否有标记处理中
		if (isMapDataProcessing.value) {
			// 获取当前可视区域
			const visibleRect = {
				left: mapPosition.value.x,
				top: mapPosition.value.y,
				right:
					mapPosition.value.x +
					(mapContainer.value?.offsetWidth || 0) / mapScale.value,
				bottom:
					mapPosition.value.y +
					(mapContainer.value?.offsetHeight || 0) / mapScale.value,
			};

			// 如果已获取地图层数据
			if (props.mapData?.map) {
				try {
					// 通过worker获取可视范围内的标记
					const visibleMarks = await mapDataManager.getVisibleMarks(
						visibleRect,
						props.mapData.map,
						mapCellSize.value,
						props.mapRatio
					);

					console.log(
						`当前可视区域内标记数量: ${visibleMarks.length}`
					);

					// 批量处理可见标记
					if (visibleMarks.length > 0) {
						// 开始批处理渲染
						processBatchMapMarks(
							visibleMarks,
							0,
							BATCH_SIZE,
							mapMarkPool,
							maps,
							isMapMode.value,
							enhancedDrawMapMarks,
							() => {
								console.log("地图标记加载完成");
								console.timeEnd("地图标记处理总时间");

								// 更新已加载标记索引
								visibleMarks.forEach((mark) => {
									loadedMapIndexes.add(
										mark.row * 1000000 + mark.col
									);
								});
							}
						);
					} else {
						console.timeEnd("地图标记处理总时间");
					}
				} catch (error) {
					console.error("加载可视标记出错:", error);
				}
			}
		}
	};

	// 优化 drawMapMarks 函数，支持大数据量渲染
	const enhancedDrawMapMarks = () => {
		if (!mapDrawCanvas.value || !props.mapData?.map) return;

		// 使用缓存的上下文
		const ctx =
			mapDrawContext.value ||
			mapDrawCanvas.value.getContext("2d", { willReadFrequently: true });
		if (!ctx) return;

		// 获取地图数据尺寸
		const rows = props.mapData.size.rowCnt;
		const cols = props.mapData.size.colCnt;

		// 计算总地图尺寸
		const totalMapWidth = cols * mapCellSize.value * props.mapRatio;
		const totalMapHeight = rows * mapCellSize.value;

		// 确保画布尺寸与地图层画布一致
		if (
			mapDrawCanvas.value.width !== totalMapWidth ||
			mapDrawCanvas.value.height !== totalMapHeight
		) {
			mapDrawCanvas.value.width = totalMapWidth;
			mapDrawCanvas.value.height = totalMapHeight;
		}

		// 清除画布
		ctx.clearRect(0, 0, totalMapWidth, totalMapHeight);

		// 保存当前状态
		ctx.save();

		// 创建临时数据结构存储每个单元格的填充和边界信息
		const cellsData = new Map();

		// 预处理所有标记，确保填充和边界信息一致
		maps.value.forEach((mark) => {
			const key = `${mark.row},${mark.col}`;
			cellsData.set(key, {
				row: mark.row,
				col: mark.col,
				fillColor: mark.fillColor || "rgba(255, 0, 0, 0.3)",
				strokeColor: mark.strokeColor || "red",
			});
		});

		// 绘制所有单元格的填充
		ctx.save();

		// 按行处理单元格，合并相邻相同颜色的单元格
		for (let row = 0; row < rows; row++) {
			// 收集该行所有单元格
			let rowCells = [];
			for (let col = 0; col < cols; col++) {
				const key = `${row},${col}`;
				if (cellsData.has(key)) {
					rowCells.push({
						col,
						...cellsData.get(key),
					});
				}
			}

			if (rowCells.length === 0) continue;

			// 按列排序
			rowCells.sort((a, b) => a.col - b.col);

			// 合并相邻相同颜色的单元格
			let currentColor = null;
			let startCol = -1;
			let endCol = -1;

			for (let i = 0; i <= rowCells.length; i++) {
				const cell: any = i < rowCells.length ? rowCells[i] : null;

				if (
					currentColor === null ||
					cell === null ||
					cell.col !== endCol + 1 ||
					cell.fillColor !== currentColor
				) {
					// 绘制之前的一组
					if (currentColor !== null && endCol >= startCol) {
						const x = startCol * mapCellSize.value * props.mapRatio;
						const y = row * mapCellSize.value;
						const width =
							(endCol - startCol + 1) *
							mapCellSize.value *
							props.mapRatio;
						const height = mapCellSize.value;

						ctx.fillStyle = currentColor;
						ctx.fillRect(x, y, width, height);
					}

					// 开始新的一组
					if (cell !== null) {
						currentColor = cell.fillColor;
						startCol = cell.col;
						endCol = cell.col;
					} else {
						currentColor = null;
					}
				} else {
					// 继续当前一组
					endCol = cell.col;
				}
			}
		}

		ctx.restore();

		// 直接绘制边界，不使用Worker异步处理
		ctx.save();

		// 创建一个辅助函数检查单元格是否存在
		const cellExists = (row, col) => {
			return cellsData.has(`${row},${col}`);
		};

		// 绘制每个单元格的边界
		cellsData.forEach((cell, key) => {
			const row = cell.row;
			const col = cell.col;
			const x = col * mapCellSize.value * props.mapRatio;
			const y = row * mapCellSize.value;
			const width = mapCellSize.value * props.mapRatio;
			const height = mapCellSize.value;

			ctx.strokeStyle = cell.strokeColor;
			ctx.lineWidth = 1;
			ctx.beginPath();

			// 只绘制外部边界
			// 检查上方单元格
			if (
				!cellExists(row - 1, col) ||
				cellsData.get(`${row - 1},${col}`)?.fillColor !== cell.fillColor
			) {
				ctx.moveTo(x, y);
				ctx.lineTo(x + width, y);
			}

			// 检查下方单元格
			if (
				!cellExists(row + 1, col) ||
				cellsData.get(`${row + 1},${col}`)?.fillColor !== cell.fillColor
			) {
				ctx.moveTo(x, y + height);
				ctx.lineTo(x + width, y + height);
			}

			// 检查左侧单元格
			if (
				!cellExists(row, col - 1) ||
				cellsData.get(`${row},${col - 1}`)?.fillColor !== cell.fillColor
			) {
				ctx.moveTo(x, y);
				ctx.lineTo(x, y + height);
			}

			// 检查右侧单元格
			if (
				!cellExists(row, col + 1) ||
				cellsData.get(`${row},${col + 1}`)?.fillColor !== cell.fillColor
			) {
				ctx.moveTo(x + width, y);
				ctx.lineTo(x + width, y + height);
			}

			ctx.stroke();
		});

		ctx.restore();

		// 绘制当前选区预览
		if (
			mapIsDrawing.value &&
			mapSelectionStart.value &&
			mapSelectionCurrent.value &&
			currentMapTool.value === "section"
		) {
			const startRow = Math.min(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const startCol = Math.min(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);
			const endRow = Math.max(
				mapSelectionStart.value.row,
				mapSelectionCurrent.value.row
			);
			const endCol = Math.max(
				mapSelectionStart.value.col,
				mapSelectionCurrent.value.col
			);

			// 计算选区位置和大小
			const x = startCol * mapCellSize.value * props.mapRatio;
			const y = startRow * mapCellSize.value;
			const width =
				(endCol - startCol + 1) * mapCellSize.value * props.mapRatio;
			const height = (endRow - startRow + 1) * mapCellSize.value;

			// 先只绘制填充
			ctx.fillStyle = drawConfig.fill || "rgba(255, 0, 0, 0.3)";
			ctx.beginPath();
			ctx.rect(x, y, width, height);
			ctx.fill();

			// 单独绘制边框
			ctx.strokeStyle = drawConfig.stroke || "red";
			ctx.lineWidth = 1.5; // 稍微粗一点的边框以示区分
			ctx.strokeRect(x, y, width, height);
		}

		// 恢复状态
		ctx.restore();
	};

	if (
		typeof MapWorkerManager.prototype.calculateSelectedArea !== "function"
	) {
		MapWorkerManager.prototype.calculateSelectedArea = function (
			startRow: number,
			startCol: number,
			endRow: number,
			endCol: number,
			callback: (validCells: { row: number; col: number }[]) => void
		) {
			this.callWorker(
				"calculateSelectedArea",
				{ startRow, startCol, endRow, endCol },
				callback
			);
		};
	}

	return {
		mapContainer,
		mapLayerCanvas,
		mapDrawCanvas,
		mapLayerContext,
		mapDrawContext,
		mapScale,
		mapMinScale,
		mapPosition,
		mapDragging,
		mapDragStartPosition,
		mapDragLastPosition,
		mapCellSize,
		mapIsDrawing,
		mapSelectionStart,
		mapSelectionCurrent,
		maps,
		mapWorker,
		mapRenderPending,
		showMapTooltip,
		mapTooltipText,
		mapTooltipStyle,
		mapHoverTimer,
		mapHoverPosition,
		mapMarkPool,
		mapLayerCache,
		lastMapRenderScale,
		mapChunkSize,
		mapDataManager,
		loadedMapIndexes,
		BATCH_SIZE,
		isMapMode,
		isMapDataProcessing,
		currentMapTool,
		mapTools,
		handleMapToolClick,
		initMap,
		getVisibleMapRect,
		drawMapLayer,
		drawLowResolutionMap,
		drawHighResolutionMap,
		drawGridLines,
		clearMapLayerCache,
		drawMapMarks,
		handleMapClick,
		handleMapClickMode,
		handleMapSectionMode,
		handleMapRowMode,
		handleMapColMode,
		optimizeMapRendering,
		handleMapWheel,
		handleMapDragStart,
		handleMapMouseMove,
		handleMapMouseUp,
		clearMapTooltip,
		handleViewportChange,
		loadVisibleMapMarks,
		enhancedDrawMapMarks,
	};
};
