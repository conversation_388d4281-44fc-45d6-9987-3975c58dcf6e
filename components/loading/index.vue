<template>
  <div v-if="visible" :class="['loading-wrapper', { 'is-fullscreen': !target }]" :style="style">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  target: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    default: '加载中...'
  }
})

const style = computed(() => {
  if (!props.target) return {}
  const targetElement = document.querySelector(props.target)
  if (!targetElement) return {}

  const { top, left, width, height } = targetElement.getBoundingClientRect()

  return {
    top: `${top}px`,
    left: `${left}px`,
    width: `${width}px`,
    height: `${height}px`
  }
})
</script>

<style scoped>
.loading-wrapper {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.is-fullscreen {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-text {
  margin-top: 10px;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
