export type ModeType = {
	mode: string;
	isActive: boolean;
	value: number;
};

export type AxesType = {
	axis: string;
	isActive: boolean;
	isEnabled: boolean;
	distance: number;
	direction: string;
};

export type CoordinateType = {
	X: number;
	Y: number;
	T: number;
	Z: number;
	C: number;
	F: number;
};

export type AxesParamsType = {
	axis: string;
	distance: number;
	direction: string;
};

export type ParamsType = {
	mode: number;
	axes: AxesParamsType[];
};

export type AxesListItemType = {
	id: number;
	name: string;
	[key: string]: any;
};
