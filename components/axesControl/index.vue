<template>
  <div class="axes-container">
    <div class="item-container width-10">
      <template v-if="isAxes">
        <div v-for="item in mode" :key="item.mode"
          :class="item.isActive ? 'item height-40 cw-active btn' : 'item height-40 btn'" @click="handleModeClick(item)">
          {{ item.mode }}
        </div>
      </template>
      <template v-else>
        <div class="height-40"></div>
        <div class="height-40"></div>
      </template>
      <div class="item plat-item" @click="() => { isAxes = !isAxes }">{{ !isAxes ? t("control.axes.axes") :
        t("control.axes.plate")
        }}</div>
    </div>
    <template v-if="isAxes">
      <div class="item-container width-10">
        <div v-for="item in axes" :key="item.axis" :class="item.isEnabled
          ? item.isActive
            ? 'item height-22 cw-active btn'
            : 'item height-22 btn'
          : 'item height-22 cw-disable btn'
          " @click="handleAxisClick(item)">
          {{ item.axis }}
        </div>
      </div>
      <div class="item-container width-25">
        <div v-for="item in axes" :key="item.axis" class="item height-22 border-no">
          <el-input v-model="item.distance" type="number" step="0.001" min="0" :disabled="!item.isEnabled"></el-input>
        </div>
      </div>
      <div class="item-container width-10">
        <div v-for="item in axes" :key="item.axis" :class="item.isEnabled && item.isActive
          ? item.direction === '-'
            ? 'item height-22 cw-active btn'
            : 'item height-22 btn'
          : 'item height-22 cw-disable btn'
          " @click="handleLeftClick(item)">
          -
        </div>
      </div>
      <div class="item-container width-10">
        <div v-for="item in axes" :key="item.axis" :class="item.isEnabled && item.isActive
          ? item.direction === '+'
            ? 'item height-22 cw-active btn'
            : 'item height-22 btn'
          : 'item height-22 cw-disable btn'
          " @click="handleRightClick(item)">
          +
        </div>
      </div>
      <div class="item-container width-20">
        <div v-for="axis in axes" :key="axis.axis" class="item height-22 is-special">
          {{ props.coordinate[axis.axis as keyof typeof props.coordinate] || "-" }}
        </div>
      </div>
      <div class="item-container width-15">
        <div class="item height-48 btn" @click="handleMove">
          {{ t("control.axes.move") }}
        </div>
        <div class="item height-48 btn" @click="handleClear">
          {{ t("control.axes.clear") }}
        </div>
      </div>
      <div class="item-container width-15">
        <div class="item height-48 btn" @click="handleStop">
          {{ t("control.axes.stop") }}
        </div>
        <div class="item height-48 btn" @click="handlePick">
          {{ t("control.axes.get") }}
        </div>
      </div>
    </template>
    <template v-else>
      <div class="plat-container">
        <div class="controller">
          <div v-for="btn in directionButtons" :key="btn.key" :class="[btn.value !== 9 ? 'sector' : '', btn.key]"
            @mousedown="handlePtzControl(btn.value)">
            <el-icon v-if="btn.value !== 9">
              <i-ep-caret-left />
            </el-icon>
            <el-icon v-else>
              <i-ep-refresh />
            </el-icon>
          </div>
        </div>
        <div class="z-controller" v-if="props.isZControl">
          <CWZControl @zControl="handleZ1Control" title="ScanHeadZ" @stop="handleZ1Stop" />
        </div>
        <div class="z-controller" v-if="props.isZControl">
          <CWZControl @zControl="handleZ2Control" title="ProbeZ" @stop="handleZ2Stop" />
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, reactive } from "vue"
import { useI18n } from "vue-i18n"
import type {
  ModeType,
  AxesType,
  CoordinateType,
  AxesListItemType,
} from "./type"
import CWZControl from "../zControl/index.vue"


interface DirectionButton {
  label: string
  key: string
  value: number
}

const { t } = useI18n()

const mode = ref<ModeType[]>([
  {
    mode: t("control.axes.absolute"),
    isActive: true,
    value: 0,
  },
  {
    mode: t("control.axes.relative"),
    isActive: false,
    value: 1,
  },
])

const props = defineProps<{
  coordinate: CoordinateType
  axes: AxesListItemType[],
  isZControl: boolean
}>()

const initAxes: AxesType[] = props.axes
  .filter((v) => v.enable)
  .map((v) => ({
    axis: v.name,
    isActive: false,
    isEnabled: true,
    distance: 0,
    direction: "",
  }))

const axes = ref<AxesType[]>([...initAxes])
const activeMode = ref<number>(0)
const isAxes = ref<boolean>(true)

const isControlActive = ref<boolean>(false)

const directionButtons = reactive<DirectionButton[]>([
  {
    label: '上',
    key: 'up',
    value: 1
  },
  {
    label: '右上',
    key: 'up-right',
    value: 2
  },
  {
    label: '右',
    key: 'right',
    value: 3
  },
  {
    label: '右下',
    key: 'down-right',
    value: 4
  },
  {
    label: '下',
    key: 'down',
    value: 5
  },
  {
    label: '左下',
    key: 'down-left',
    value: 6
  },
  {
    label: '左',
    key: 'left',
    value: 7
  },
  {
    label: '左上',
    key: 'up-left',
    value: 8
  },
  {
    label: '中心',
    key: 'center',
    value: 9
  },
])

const handleModeClick = (item: ModeType) => {
  mode.value.forEach((v, i, arr) => (arr[i].isActive = false))
  item.isActive = true
  activeMode.value = item.value
  if (item.value === 0) {
    axes.value.forEach((v) => {
      v.direction = ""
    })
  }
}

const handleAxisClick = (item: AxesType) => {
  if (!item.isEnabled) return false
  item.isActive = !item.isActive
}

const handleLeftClick = (item: AxesType) => {
  if (!item.isActive) return false
  if (item.direction !== "-") {
    item.direction = "-"
  } else {
    item.direction = ""
  }
}

const handleRightClick = (item: AxesType) => {
  if (!item.isActive) return false
  if (item.direction !== "+") {
    item.direction = "+"
  } else {
    item.direction = ""
  }
}

const emit = defineEmits(["move", "stop", "ptzControl", 'scanHeadZControl', 'scanHeadZStop', 'probeZControl', 'probeZStop'])

const handleMove = () => {
  const activeAxes = axes.value.filter((v) => v.isActive && (activeMode.value === 0 ? true : v.direction)).map((v) => {
    if (v.direction === "-") {
      return { axis: v.axis, distance: -v.distance }
    } else {
      return { axis: v.axis, distance: Number(v.distance) }
    }
  })
  emit("move", { mode: activeMode.value, axes: activeAxes })
}

const handleStop = () => {
  const activeAxes = axes.value.filter((v) => v.isActive).map((v) => ({ axis: v.axis }))
  emit("stop", { axes: activeAxes })
}

const handleClear = () => {
  mode.value.forEach((v, i) => {
    if (i === 0) {
      v.isActive = true
    } else {
      v.isActive = false
    }
  })

  axes.value = initAxes.map(axis => ({
    ...axis,
    isActive: false,
    distance: 0,
    direction: ""
  }))
}

const handlePick = () => {
  axes.value.forEach((v) => {
    if (v.axis === "X") {
      v.distance = props.coordinate.X
    } else if (v.axis === "Y") {
      v.distance = props.coordinate.Y
    } else if (v.axis === "T") {
      v.distance = props.coordinate.T
    } else if (v.axis === "Z") {
      v.distance = props.coordinate.Z
    }
  })
}

const handlePtzControl = (value: number) => {
  isControlActive.value = true
  emit('ptzControl', value)
}

const stopDirectionMove = () => {
  if (isControlActive.value) {
    emit('ptzControl', 0) // 发送停止信号
  }

  isControlActive.value = false
}

const handleZ1Control = (direction: number, value: number) => {
  emit('scanHeadZControl', direction, value)
}

const handleZ1Stop = () => {
  emit('scanHeadZStop')
}

const handleZ2Control = (direction: number, value: number) => {
  emit('probeZControl', direction, value)
}

const handleZ2Stop = () => {
  emit('probeZStop')
}

onMounted(() => {
  window.addEventListener('mouseup', stopDirectionMove)
})

onUnmounted(() => {
  window.removeEventListener('mouseup', stopDirectionMove)
})
</script>

<style lang="less" scoped>
.axes-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: @cw-gap;
  }

  .item {
    width: 100%;
    height: 48%;
    border-radius: @cw-gap;
    border: 1px solid @cw-border-color;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-sizing: border-box;

    .el-input {
      height: 24px;
    }
  }

  .plat-item {
    flex: 1;
  }

  .btn {
    transition: all 0.3s ease;

    &:hover {
      background-color: @cw-border-color-active;
      color: @cw-font-color-title;
    }
  }

  .width-10 {
    width: 8%;
  }

  .width-15 {
    width: 12%;
  }

  .width-20 {
    width: 16%;
  }

  .width-25 {
    width: 20%;
  }

  .height-22 {
    height: 22%;
  }

  .height-40 {
    height: 40%;
  }

  .height-48 {
    height: 48%;
  }

  .border-no {
    border: none;
  }

  .is-special {
    border: none;
    border-radius: 2px;
    background-color: @cw-background-color;
    color: @cw-font-color-title;
  }

  .plat-container {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .controller {
    position: relative;
    width: 110px;
    height: 110px;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: rotate(22deg);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    z-index: 9;
  }

  .z-controller {
    max-width: 80px;
  }

  .sector {
    width: 110px;
    height: 110px;
    background-color: @cw-font-color;
    clip-path: polygon(50% 50%, 100% 50%, 100% 3%);
    border-radius: 50%;
    position: absolute;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(110, 180, 237, 0.8);
    }

    .el-icon {
      position: absolute;
      right: 10px;
      top: 34px;
      transform: rotate(-200deg);
      font-size: 18px;
      color: @cw-background-color;
    }
  }

  /* 各个方向的扇形按钮 */
  .up {
    transform: rotate(270deg);
  }

  .up-right {
    transform: rotate(315deg);
  }

  .right {
    transform: rotate(0deg);
  }

  .down-right {
    transform: rotate(45deg);
  }

  .down {
    transform: rotate(90deg);
  }

  .down-left {
    transform: rotate(135deg);
  }

  .left {
    transform: rotate(180deg);
  }

  .up-left {
    transform: rotate(225deg);
  }

  .center {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: @cw-background-color;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: rgba(110, 180, 237, 0.8);
    }

    .el-icon {
      font-size: 20px;
      transform: rotate(-22deg);
    }
  }

  .coordinates-display {
    display: flex;
    flex-direction: column;
    gap: @cw-gap;
    margin-left: 30px;
  }

  .input-row {
    width: 100%;
    max-width: 160px;
    display: flex;
    align-items: center;
    gap: @cw-gap;
  }

  .axis-label {
    width: 15px;
    color: @cw-font-color;
    font-weight: bold;
  }

  .coordinate-value {
    min-width: 120px;
    padding: 4px 10px;
    border: none;
    border-radius: 4px;
    background-color: @cw-background-color;
    color: @cw-font-color-title;
    text-align: center;
  }
}
</style>
