<template>
  <div class="camera-container">
    <div v-for="(item, index) in camera" :key="index" class="item-container"
      :style="{ width: `${60 / camera.length}%` }">
      <div :class="item.name ? (item.isActive ? 'item cw-active' : 'item') : 'item cw-disable'"
        @click="handleCameraClick(item)">{{ item.idx }}</div>
      <div :class="item.name ? 'name' : 'name light'">{{ item.name || '-' }}</div>
    </div>
    <div class="item-container" style="width: 40%;">
      <div class="opt">
        <div v-for="item in mode" :key="item.type" :class="item.isActive ? 'mode cw-active' : 'mode'"
          @click="handleModeClick(item)">{{ item.type }}</div>
      </div>
      <div class="config" @click="handleOpenConfig">
        <span class="iconfont icon-shezhi"></span>
      </div>
    </div>
  </div>
  <el-dialog :title="t('control.camera.title')" v-model="isOpen" width="30%">
    <el-form :model="cameraParams" ref="config" label-width="150px">
      <el-form-item prop="gain" :label="t('control.camera.gain')">
        <el-input v-model="cameraParams.gain" type="number"
          :placeholder="t('common.enterPrefix') + t('control.camera.gain')"></el-input>
      </el-form-item>
      <el-form-item prop="exposureTime" :label="t('control.camera.expose')">
        <el-input v-model="cameraParams.exposureTime" type="number"
          :placeholder="t('common.enterPrefix') + t('control.camera.expose')"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button @click="handleOk">{{ t('common.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElButton, ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import type { CameraItemType, ModeItemType, CameraParamsType, ModeParamsType, CameraListItemType } from './type'

const { t } = useI18n()

const props = defineProps<{
  cameraList: CameraListItemType[]
}>()

const initCamera = props.cameraList.map(item => ({
  idx: item.num,
  name: item.name,
  isActive: false,
}))

const camera = ref<CameraItemType[]>([...initCamera])

const mode = ref<ModeItemType[]>([
  {
    idx: 1,
    type: t('control.camera.trigger'),
    isActive: false,
  },
  {
    idx: 2,
    type: t('control.camera.continuous'),
    isActive: false,
  },
  {
    idx: 3,
    type: t('control.camera.internal'),
    isActive: false,
  }
])


const isOpen = ref(false)
const config = ref<FormInstance | null>(null)

const modeParams = ref<ModeParamsType>({
  mode: 0,
  camIdx: 0,
})

const cameraParams = defineModel<CameraParamsType>('cameraParams', {
  required: true,
  default: () => ({
    camIdx: 0,
    gain: 0,
    exposureTime: 0,
  }),
})

const emit = defineEmits(['send', 'change', 'get', 'set'])

const handleCameraClick = (item: CameraItemType) => {
  camera.value.forEach((v, i, arr) => { arr[i].isActive = false })
  item.isActive = true
  modeParams.value.camIdx = item.idx
  cameraParams.value.camIdx = item.idx
  emit('send', item.idx)
}

const handleModeClick = (item: ModeItemType) => {
  if (!modeParams.value.camIdx) {
    ElMessage.warning(t('control.camera.pleaseSelectCamera'))
    return
  }
  mode.value.forEach((v, i, arr) => { arr[i].isActive = false })
  item.isActive = true
  modeParams.value.mode = item.idx
  emit('change', modeParams.value)
}

const handleOpenConfig = () => {
  console.log(cameraParams.value)
  if (!cameraParams.value.camIdx) {
    ElMessage.warning(t('control.camera.pleaseSelectCamera'))
    return
  }
  isOpen.value = true
  emit('get', cameraParams.value.camIdx)
}

const handleCancel = () => {
  isOpen.value = false
}

const handleOk = () => {
  emit('set', cameraParams.value)
  isOpen.value = false
}

</script>

<style lang="less" scoped>
.camera-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: @cw-gap;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 80%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .opt {
      width: 100%;
      height: 80%;
      display: flex;
      flex-direction: column;
      gap: @cw-gap;

      .mode {
        width: 100%;
        height: 30%;
        border: 1px solid @cw-border-color;
        border-radius: @cw-gap;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: @cw-border-color-active;
          color: @cw-font-color-title;
        }
      }
    }

    .config {
      width: 100%;
      height: 15%;
      display: flex;
      color: @cw-font-color;
      border: 1px solid @cw-border-color;
      justify-content: center;
      align-items: center;
      cursor: pointer;

    }

  }
}
</style>
