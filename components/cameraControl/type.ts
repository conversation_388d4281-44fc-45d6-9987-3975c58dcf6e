export interface CameraItemType {
	idx: number;
	name: string;
	isActive: boolean;
}

export interface ModeItemType {
	idx: number;
	type: string;
	isActive: boolean;
}

export interface CameraParamsType {
	camIdx: number;
	gain: number;
	exposureTime: number;
}

export interface ModeParamsType {
	mode: number;
	camIdx: number;
}

export interface CameraListItemType {
	num: number;
	name: string;
	enable: boolean;
	[key: string]: any;
}
