<template>
  <div class="axes-panel-control">
    <div class="stage-container">
      <div class="stage-panel" ref="stagePanel" @click="handlePanelClick">
        <!-- 中心黑色十字标 -->
        <div class="center-cross"></div>
        <!-- 当前位置红色十字标 -->
        <div class="current-position" :style="currentPositionStyle">
          <div class="position-cross"></div>
        </div>
      </div>
    </div>
    <div class="tips-container">
      <div class="tips-container-item">
        <div class="tips-container-item-label">X(mm)</div>
        <div class="tips-container-item-value">{{ props.stage.x.toFixed(4) }}</div>
      </div>
      <div class="tips-container-item">
        <div class="tips-container-item-label">Y(mm)</div>
        <div class="tips-container-item-value">{{ props.stage.y.toFixed(4) }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'

const props = defineProps<{
  stage: {
    x: number
    y: number
  }
  width: number
  height: number
  center: number //1: 左上角, 2: 右上角, 3: 左下角, 4: 右下角 为原点
}>()

const emit = defineEmits<{
  (e: 'update:stage', stage: { x: number, y: number }): void
}>()

const stagePanel = ref<HTMLElement | null>(null)

// 计算当前位置的样式
const currentPositionStyle = computed(() => {
  if (!stagePanel.value) return {}

  // 将实际坐标映射到面板像素坐标
  let xPercent: number, yPercent: number

  // 根据不同的原点设置计算方式
  switch (props.center) {
    case 1: // 左上角
      xPercent = (props.stage.x / props.width) * 100
      yPercent = (props.stage.y / props.height) * 100
      break
    case 2: // 右上角
      xPercent = 100 - (props.stage.x / props.width) * 100
      yPercent = (props.stage.y / props.height) * 100
      break
    case 3: // 左下角
      xPercent = (props.stage.x / props.width) * 100
      yPercent = 100 - (props.stage.y / props.height) * 100
      break
    case 4: // 右下角
      xPercent = 100 - (props.stage.x / props.width) * 100
      yPercent = 100 - (props.stage.y / props.height) * 100
      break
    default:
      xPercent = (props.stage.x / props.width) * 100
      yPercent = (props.stage.y / props.height) * 100
  }

  return {
    left: `${xPercent}%`,
    top: `${yPercent}%`
  }
})

// 处理面板点击
const handlePanelClick = (event: MouseEvent) => {
  if (!stagePanel.value) return

  const rect = stagePanel.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 计算点击位置相对于面板的百分比
  const xPercent = x / rect.width
  const yPercent = y / rect.height

  // 根据不同的原点计算实际坐标
  let realX, realY

  switch (props.center) {
    case 1: // 左上角
      realX = xPercent * props.width
      realY = yPercent * props.height
      break
    case 2: // 右上角
      realX = (1 - xPercent) * props.width
      realY = yPercent * props.height
      break
    case 3: // 左下角
      realX = xPercent * props.width
      realY = (1 - yPercent) * props.height
      break
    case 4: // 右下角
      realX = (1 - xPercent) * props.width
      realY = (1 - yPercent) * props.height
      break
    default:
      realX = xPercent * props.width
      realY = yPercent * props.height
  }

  // 发送更新事件
  emit('update:stage', {
    x: realX,
    y: realY
  })
}
</script>

<style scoped lang="less">
.axes-panel-control {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: @cw-gap;
  box-sizing: border-box;

  .stage-container {
    width: 200px;
    height: 80%;
    box-shadow: @cw-card-shadow;
    border-radius: @cw-border-radius;
    background-color: @cw-card-color;
    padding: @cw-gap;
    box-sizing: border-box;

    .stage-panel {
      position: relative;
      width: 100%;
      height: 100%;
      cursor: pointer;
      user-select: none;
      border: 1px solid @cw-border-color;
      background-color: @cw-background-color;

      // 中心黑色十字标
      .center-cross {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;

        &:before,
        &:after {
          content: '';
          position: absolute;
          background-color: @cw-font-color;
        }

        &:before {
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          transform: translateY(-50%);
        }

        &:after {
          left: 50%;
          top: 0;
          bottom: 0;
          width: 1px;
          transform: translateX(-50%);
        }
      }

      // 当前位置红色十字标
      .current-position {
        position: absolute;
        transform: translate(-50%, -50%);

        .position-cross {
          width: 10px;
          height: 10px;
          position: relative;

          &:before,
          &:after {
            content: '';
            position: absolute;
            background-color: #f00;
          }

          &:before {
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
          }

          &:after {
            left: 50%;
            top: 0;
            bottom: 0;
            width: 1px;
          }
        }
      }
    }
  }

  .tips-container {
    width: 200px;
    height: 20%;
    background-color: @cw-card-color;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: @cw-gap;
    padding: @cw-gap;
    border-radius: @cw-border-radius;
    box-shadow: @cw-card-shadow;
    box-sizing: border-box;

    .tips-container-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .tips-container-item-label {
        font-size: 12px;
        font-weight: 600;
        color: @cw-font-color-title;
      }

      .tips-container-item-value {
        font-size: 14px;
        color: @cw-font-color;
      }
    }
  }
}
</style>
