<template>
  <div :style="{ display: props.nodeData.data.type === 1 ? 'block' : 'inline-block' }">
    <div class="rules-item-title" :class="props.nodeData.data.type === 1 ? 'itemTitle' : 'paramsTitle'">{{
      props.nodeData?.data.label }}</div>
    <div class="rules-content" v-if="props.nodeData">

      <el-input v-if="props.nodeData.data.type === 2" type="number"
        @change="(val) => { changeValue(val, props.nodeData.data) }" v-model.number="props.nodeData.data.value" />

      <el-select v-else-if="props.nodeData.data.type === 3" v-model="props.nodeData.data.value"
        @change="(val) => { changeValue(val, props.nodeData.data) }">
        <el-option v-for="(item, index) in props.nodeData.data.info" :key="index" :value="item"></el-option>
      </el-select>
      <el-select v-else-if="props.nodeData.data.type === 4" v-model="props.nodeData.data.value" multiple collapse-tags
        @change="(val) => { changeValue(val, props.nodeData.data) }">
        <el-option v-for="(item, index) in props.nodeData.data.info" :key="index" :value="item"></el-option>
      </el-select>
      <el-switch v-else-if="props.nodeData.data.type === 5" @change="(val) => { changeValue(val, props.nodeData.data) }"
        v-model="props.nodeData.data.value" active-value="1" inactive-value="0" width="50"></el-switch>

      <div v-if="props.nodeData.data.type === 1 && props.nodeData.children && props.nodeData.children.length > 0">
        <CWParamsTree v-for="child in props.nodeData.children" :key="child.data.id" :node-data="child"
          @updateParam="handleChildUpdate" />
      </div>

    </div>
  </div>
</template>
<script lang="ts" setup>

import { defineComponent, PropType, onMounted } from 'vue'
import { ElMessage } from "element-plus"

import { CWParamsTree } from '@cw/components'
import { useRouter } from "vue-router"
import { useI18n } from "vue-i18n"
const router = useRouter()
const { t } = useI18n()
interface Data {
  id?: number
  value: string
  label: string
  name: string
  tempId: number
  orderNum: number
  parentId: number
  type: number // 1标题 2参数,
  info: string
  isEdit: boolean
}
interface Tree {
  data: Data
  children?: Tree[]
}

const emit = defineEmits(['updateParam'])
const changeValue = (value: string, data: Data) => {
  // console.log('changeValue', value, data)
  emit('updateParam', value, data)
}
const handleChildUpdate = (value: string, data: Data) => {
  // console.log('handleChildUpdate', value);
  emit('updateParam', value, data)
}
onMounted(() => {
  // console.log(props.nodeData)
})
const props = defineProps({
  nodeData: Object as PropType<Tree>,
})


</script>
<style lang="less" scoped>
// .rules-container {
// width: 100%;
// // height: 100%;
// position: relative;
// box-sizing: border-box;
// background-color: @cw-card-color;
// padding: @cw-gap;

.rules-btn {
  position: absolute;
  bottom: @cw-gap;
  right: @cw-gap;
}

.rules-item {
  margin-bottom: @cw-gap;

  &-title {
    font-weight: 600;
    color: @cw-font-color-title;
    margin-bottom: @cw-gap;
  }


}

.paramsTitle {
  font-weight: 400;
  display: inline-block;
  margin-bottom: 12px;
}

.rules-content {
  display: inline-block;
  flex-wrap: wrap;
  vertical-align: middle;
  gap: @cw-gap;
  // margin-bottom: 12px;
  margin-left: 20px;
  margin-right: 10px;

  :deep(.el-select),
  :deep(.el-input) {
    width: 200px;
  }

}

.rule-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .label {
    width: 120px;
    margin-right: @cw-gap;
    text-align: center;
  }


}

:deep(input[type="number"]::-webkit-inner-spin-button),
:deep(input[type="number"]::-webkit-outer-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}


// }
</style>
