<template>
  <div class="light-container">
    <div v-for="item in lightMode" :key="item.idx" class="item-container"
      :style="{ width: `${100 / lightMode.length}%` }">
      <div :class="item.isActive ? 'mode cw-active' : 'mode'" @click="handleTriggerClick(item)">On</div>
      <div :class="!item.isActive ? 'mode cw-active' : 'mode'" @click="handleTriggerClick(item)">Off</div>
      <div :class="item.name ? 'name' : 'name light'">{{ item.name || '-' }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { ModeType } from './type'

const { t } = useI18n()

const lightMode = ref<ModeType[]>([
  {
    idx: 1,
    name: t('control.light.brightField'),
    isActive: true,
  },
  {
    idx: 2,
    name: t('control.light.darkField'),
    isActive: false,
  },
])

const emit = defineEmits(['trigger'])

const handleTriggerClick = (item: ModeType) => {
  item.isActive = !item.isActive
  if (item.isActive) {
    emit('trigger', item.idx, 2)
  } else {
    emit('trigger', item.idx, 1)
  }
}

</script>

<style lang="less" scoped>
.light-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  gap: @cw-gap;
  display: flex;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: @cw-gap;

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }

    .mode {
      width: 100%;
      height: 100%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-sizing: border-box;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

  }
}
</style>
