<template>
  <div class="oglass-container">
    <div v-for="glass in glasses" :key="glass.idx" class="item-container"
      :style="{ width: `${100 / glasses.length}%` }">
      <div :class="glass.name ? (glass.idx === props.len.idx ? 'item cw-active' : 'item') : 'item cw-disable'"
        @click="handleGlassClick(glass)">{{ glass.name || '-' }}</div>
      <div :class="glass.name ? 'name' : 'name light'">{{ glass.idx }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { OglassInfoType, Len, OglassListItemType } from './type'

const props = defineProps<{
  len: Len,
  lensList: OglassListItemType[]
}>()

const initGlasses = props.lensList.map(item => ({
  idx: item.num,
  name: item.enable ? item.name : '',
}))

const glasses = ref<OglassInfoType[]>(initGlasses)

const emit = defineEmits(['send', 'change'])

const handleGlassClick = (item: OglassInfoType) => {
  if (!item.name) return false
  emit('send', item)
  emit('change', item)
}
</script>

<style lang="less" scoped>
.oglass-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  gap: @cw-gap;
  align-items: center;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 80%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .light {
      background-color: @cw-background-color;
    }

  }


}
</style>
