<template>
  <div class="focus-container">
    <div class="item-container">
      <div class="item" @click="handleUpClick">
        <span class="iconfont icon-shangyi"></span>
      </div>
      <div class="item" @click="handleDownClick">
        <span class="iconfont icon-xiayi"></span>
      </div>
      <el-input v-model="focusParams.distance" type="number" />
    </div>
    <div class="btn-container">
      <div class="btn" @click="handleFocusClick">{{ t('control.focus.focus') }}</div>
      <div class="btn" @click="handleHomeClick">Home</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { FocusParamsType } from './type'

const { t } = useI18n()

const focusParams = ref<FocusParamsType>({
  direct: 0,
  distance: 0,
})

const emit = defineEmits(['atonce', 'home', 'move'])

const handleUpClick = () => {
  focusParams.value.direct = 0
  emit('move', focusParams.value)
}

const handleDownClick = () => {
  focusParams.value.direct = 1
  emit('move', focusParams.value)
}

const handleFocusClick = () => {
  emit('atonce')
}

const handleHomeClick = () => {
  emit('home')
}

</script>

<style lang="less" scoped>
.focus-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: @cw-font-size;
  color: @cw-font-color;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    width: 45%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 30%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    input {
      width: 100%;
      height: 20px;
    }
  }

  .btn-container {
    width: 48%;
    height: 100%;

    .btn {
      width: 100%;
      height: 30%;
      margin-bottom: 10%;
      border: 1px solid @cw-border-color;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }
  }
}
</style>
