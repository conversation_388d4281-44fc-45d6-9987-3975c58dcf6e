<template>
  <div class="zoom-container" :style="props.options.isWheel ? { overflow: 'hidden' } : { overflow: 'auto' }"
    :ref="refs.containerRef" @wheel="handleZoom" onselectstart="return false">
    <div class="zoom-content" :ref="refs.contentRef" @mousedown="handleDrag" :style="styles">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watchEffect } from 'vue'
import { getElementSize } from '@cw/logic'
import { debounce } from 'lodash'

const props = defineProps(['options'])

const refs = {
  containerRef: ref<HTMLElement | null>(null),
  contentRef: ref<HTMLElement | null>(null),
}

const zoom = ref(props.options.zoom || 1)
const scaleY = ref(props.options.zoom || 1)
const x = ref(props.options.x || 0)
const y = ref(props.options.y || 0)


const styles = computed(() => {
  return {
    transform: `translate(${x.value}px, ${y.value}px) scale(${zoom.value}, ${scaleY.value})`
  }
})

const zoomParams = reactive({
  width: 0,
  height: 0,
  x: 0,
  y: 0,
})

const dragParams = reactive({
  offsetX: 0,
  offsetY: 0,
  positionX: props.options.x || 0,
  positionY: props.options.y || 0,
})

onMounted(() => {
  if (refs.contentRef.value) {
    setTimeout(() => {
      const { x, y } = getElementSize(refs.contentRef.value)
      zoomParams.x = x
      zoomParams.y = y
    }, 100)
  }
})

// 缩放
const emit = defineEmits(['zoom-change', 'translate-change'])
const handleZoom = debounce((event) => {
  if (!props.options.isZoomable) return false
  if (props.options.isWheel) {
    event.preventDefault()

    const delta = event.deltaY > 0 ? -1 : 1
    const oldZoomX = zoom.value
    const oldScaleY = scaleY.value
    const newZoomX = (oldZoomX * 10 + delta * props.options.zoomStep * 10) / 10
    const newScaleY = (oldScaleY * 10 + delta * props.options.zoomStep * 10) / 10
    let clampedZoomX = 0
    let clampedScaleY = 0
    clampedZoomX = Math.min(Math.max(newZoomX, props.options.zoomMin), props.options.zoomMax)
    clampedScaleY = Math.min(Math.max(newScaleY, props.options.zoomMin), props.options.zoomMax)

    requestAnimationFrame(() => {
      zoom.value = clampedZoomX
      scaleY.value = clampedScaleY
      emit('zoom-change', clampedZoomX, clampedScaleY)

      if (refs.containerRef.value && refs.contentRef.value) {

        if (event.altKey) {
          const contentRect = refs.contentRef.value.getBoundingClientRect()

          // // 计算鼠标相对于内容的位置
          const mouseX = event.clientX - contentRect.left
          const mouseY = event.clientY - contentRect.top

          // 计算缩放比例变化
          const scaleChangeX = clampedZoomX / oldZoomX
          const scaleChangeY = clampedScaleY / oldScaleY

          // 计算新的位置
          const newX = x.value - (mouseX * (scaleChangeX - 1))
          const newY = y.value - (mouseY * (scaleChangeY - 1))

          x.value = newX
          y.value = newY
        }

      }
    })
  }
}, 16)

// 拖拽
const handleDrag = (event: MouseEvent) => {
  if (!props.options?.isDragable || !refs.contentRef.value) return false

  let offsetX = 0
  let offsetY = 0
  let isDragging = false

  dragParams.positionX = Number(x.value) || 0
  dragParams.positionY = Number(y.value) || 0

  offsetX = event.clientX
  offsetY = event.clientY

  isDragging = true
  refs.contentRef.value.style.cursor = 'grab'

  const onMouseMove = (e: MouseEvent) => {
    if (!isDragging || !refs.contentRef.value) return

    const distanceX = e.clientX - offsetX
    const distanceY = e.clientY - offsetY

    x.value = Number(dragParams.positionX) + Number(distanceX)
    y.value = Number(dragParams.positionY) + Number(distanceY)

    refs.contentRef.value.style.cursor = 'grabbing'

    emit('translate-change', x.value, y.value)
  }

  document.addEventListener('mousemove', onMouseMove)

  const onMouseUp = (e: MouseEvent) => {
    isDragging = false
    if (refs.contentRef.value) {
      refs.contentRef.value.style.cursor = 'default'
    }
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  document.addEventListener('mouseup', onMouseUp)
}

// 控制缩放
const setScale = (update?: number, updateY?: number) => {
  zoom.value = update
  if (updateY) {
    scaleY.value = updateY
  } else {
    scaleY.value = update
  }
}

// 控制拖拽
const setTranslate = (newX: number, newY: number) => {
  x.value = newX
  y.value = newY
}

// 获取当前缩放和拖拽数据
const getTransform = () => {
  return {
    scale: Number(zoom.value) || 1,
    scaleY: Number(scaleY.value) || 1,
    translateX: Number(x.value) || 0,
    translateY: Number(y.value) || 0,
  }
}

// 禁止/启用缩放的方法
const setZoomable = (isZoomable: boolean) => {
  props.options.isDragable = isZoomable
}

// 禁止/启用拖拽的方法
const setDraggable = (isDraggable: boolean) => {
  props.options.isDragable = isDraggable
}

defineExpose({
  setScale,
  setTranslate,
  getTransform,
  setZoomable,
  setDraggable
})

</script>

<style lang="less" scoped>
.zoom-container {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;

  ::-webkit-scrollbar {
    display: none;
  }

  .zoom-content {
    display: inline-block;
    user-select: none;
    transform-origin: 0% 0%;
  }
}
</style>
