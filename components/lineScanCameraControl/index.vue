<template>
  <div class="line-scan-camera-container">
    <div v-for="camera in props.cameraList" :key="camera.num" class="item-container"
      :style="{ width: `${100 / props.cameraList.length}%` }">
      <div :class="camera.name ? (camera.num === props.camIdx ? 'item cw-active' : 'item') : 'item cw-disable'"
        @click="handleCameraClick(camera)">{{ camera.name }}</div>
      <div class='name'>{{ camera.num }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CameraListItemType } from './type'

const props = defineProps<{
  camIdx: number
  cameraList: CameraListItemType[]
}>()

const emit = defineEmits(['change'])

const handleCameraClick = (item: CameraListItemType) => {
  emit('change', item)
}
</script>

<style lang="less" scoped>
.line-scan-camera-container {
  width: 100%;
  height: 100%;
  padding: @cw-gap;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  gap: @cw-gap;
  align-items: center;
  background-color: @cw-card-color;
  box-shadow: @cw-card-shadow;
  border-radius: @cw-border-radius;

  .item-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item {
      width: 100%;
      height: 80%;
      border: 1px solid @cw-border-color;
      border-radius: @cw-gap;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      writing-mode: vertical-rl;

      &:hover {
        background-color: @cw-border-color-active;
        color: @cw-font-color-title;
      }
    }

    .name {
      width: 100%;
      height: 15%;
      background-color: @cw-background-color;
      color: @cw-font-color-title;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
