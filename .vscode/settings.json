{
	"editor.smoothScrolling": true,
	"editor.cursorBlinking": "expand",
	"editor.cursorSmoothCaretAnimation": "on",
	"workbench.list.smoothScrolling": true,

	// 添加缩进和格式化相关配置
	"editor.tabSize": 2,
	"editor.insertSpaces": true,
	"editor.detectIndentation": false,
	"editor.formatOnSave": true,
	"editor.defaultFormatter": "esbenp.prettier-vscode",
	"files.insertFinalNewline": true,
	"files.trimTrailingWhitespace": true,
	"[vue]": {
		"editor.defaultFormatter": "Vue.volar"
	},

	// 自动补全
	"editor.autoClosingBrackets": "beforeWhitespace",
	"editor.autoClosingDelete": "always",
	"editor.autoClosingOvertype": "always",
	"editor.autoClosingQuotes": "beforeWhitespace",

	// 自动删除分号
	"javascript.format.semicolons": "remove",
	"typescript.format.semicolons": "remove",

	// 国际化
	"i18n-ally.localesPaths": ["packages/**/src/lang"],
	"i18n-ally.keystyle": "nested",
	"i18n-ally.sortKeys": true,
	"i18n-ally.namespace": true,
	"i18n-ally.enabledParsers": ["json", "js", "ts"],
	"i18n-ally.sourceLanguage": "cn",
	"i18n-ally.displayLanguage": "cn",
	"i18n-ally.enabledFrameworks": ["vue", "vscode"],
	"i18n-ally.translate.engines": [
		"google-cn",
		"google",
		"deepl",
		"libretranslate"
	],

	// 自动补全
	"vue3snippets.enable-compile-vue-file-on-did-save-code": false,
	"[markdown]": {
		"editor.defaultFormatter": "DavidAnson.vscode-markdownlint"
	}
}
